// 创作者中心模拟数据

// 内容类型配置
export const contentTypes = [
  {
    code: 'prompt',
    name: 'AI Prompt',
    description: '创建AI提示词模板',
    icon: 'fas fa-magic',
    color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    features: ['参数化', '版本管理', '效果评估'],
    isHot: true
  },
  {
    code: 'mcp',
    name: 'MCP工具',
    description: '上传MCP能力包',
    icon: 'fas fa-cube',
    color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    features: ['工具集成', '权限管理', '调用统计'],
    isNew: true
  },
  {
    code: 'article',
    name: '技术文章',
    description: '分享技术经验和见解',
    icon: 'fas fa-file-alt',
    color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    features: ['Markdown', '代码高亮', '图片支持']
  },
  {
    code: 'course',
    name: '学习课程',
    description: '制作结构化学习内容',
    icon: 'fas fa-graduation-cap',
    color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    features: ['章节管理', '进度跟踪', '互动练习']
  },
  {
    code: 'tool',
    name: '工具推荐',
    description: '推荐实用开发工具',
    icon: 'fas fa-wrench',
    color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    features: ['工具评测', '使用指南', '替代方案']
  },
  {
    code: 'solution',
    name: '解决方案',
    description: '分享完整解决方案',
    icon: 'fas fa-lightbulb',
    color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    features: ['方案设计', '实施步骤', '最佳实践']
  }
]

// 模拟内容数据
export const mockContents = [
  {
    id: 1,
    title: 'ChatGPT代码审查Prompt模板',
    description: '专业的代码审查提示词，帮助识别代码问题和改进建议',
    type: 'prompt',
    status: 'published',
    views: 15600,
    likes: 1240,
    comments: 89,
    shares: 156,
    tags: ['代码审查', 'ChatGPT', '开发工具'],
    cover_image: '/api/placeholder/300/200',
    created_at: '2025-01-15T10:30:00Z',
    updated_at: '2025-01-20T14:20:00Z'
  },
  {
    id: 2,
    title: '智能客服MCP工具包',
    description: '基于MCP协议的智能客服工具，支持多轮对话和知识库检索',
    type: 'mcp',
    status: 'published',
    views: 12800,
    likes: 980,
    comments: 67,
    shares: 128,
    tags: ['MCP', '客服', 'AI助手'],
    cover_image: '/api/placeholder/300/200',
    created_at: '2025-01-18T09:15:00Z',
    updated_at: '2025-01-22T11:45:00Z'
  },
  {
    id: 3,
    title: 'Vue 3 + AI 开发实战指南',
    description: '详细介绍如何在Vue 3项目中集成AI功能，包含完整示例代码',
    type: 'article',
    status: 'published',
    views: 11200,
    likes: 856,
    comments: 45,
    shares: 89,
    tags: ['Vue3', 'AI集成', '前端开发'],
    cover_image: '/api/placeholder/300/200',
    created_at: '2025-01-20T16:00:00Z',
    updated_at: '2025-01-21T10:30:00Z'
  },
  {
    id: 4,
    title: 'AI Agent开发入门课程',
    description: '从零开始学习AI Agent开发，包含理论基础和实践项目',
    type: 'course',
    status: 'reviewing',
    views: 9800,
    likes: 723,
    comments: 34,
    shares: 67,
    tags: ['AI Agent', '入门教程', '实战项目'],
    cover_image: '/api/placeholder/300/200',
    created_at: '2025-01-12T14:20:00Z',
    updated_at: '2025-01-19T09:10:00Z'
  },
  {
    id: 5,
    title: '10个必备AI开发工具推荐',
    description: '精选10个AI开发中最实用的工具，提高开发效率',
    type: 'tool',
    status: 'published',
    views: 8900,
    likes: 654,
    comments: 28,
    shares: 45,
    tags: ['开发工具', 'AI开发', '效率提升'],
    cover_image: '/api/placeholder/300/200',
    created_at: '2025-01-22T11:00:00Z',
    updated_at: '2025-01-22T11:00:00Z'
  },
  {
    id: 6,
    title: '企业级AI聊天机器人解决方案',
    description: '完整的企业级AI聊天机器人架构设计和实施方案',
    type: 'solution',
    status: 'draft',
    views: 0,
    likes: 0,
    comments: 0,
    shares: 0,
    tags: ['企业级', '聊天机器人', '架构设计'],
    cover_image: null,
    created_at: '2025-01-23T15:30:00Z',
    updated_at: '2025-01-23T15:30:00Z'
  }
]

// 仪表板统计数据
export const dashboardStats = {
  totalViews: 125000,
  totalLikes: 8900,
  totalBookmarks: 3240,
  totalContents: 45,
  viewsChange: 12.5,
  likesChange: 8.3,
  bookmarksChange: 6.8,
  contentsChange: 3
}

// 内容表现趋势数据
export const chartData = {
  '7d': {
    labels: ['1天前', '2天前', '3天前', '4天前', '5天前', '6天前', '7天前'],
    datasets: [
      {
        label: '浏览量',
        data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
        borderColor: '#4f46e5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4
      },
      {
        label: '点赞数',
        data: [65, 120, 180, 280, 150, 200, 320],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4
      },
      {
        label: '收藏数',
        data: [28, 48, 75, 120, 65, 85, 140],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4
      }
    ]
  },
  '30d': {
    labels: ['第1周', '第2周', '第3周', '第4周'],
    datasets: [
      {
        label: '浏览量',
        data: [15000, 22000, 18000, 25000],
        borderColor: '#4f46e5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4
      },
      {
        label: '点赞数',
        data: [850, 1200, 980, 1400],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4
      },
      {
        label: '收藏数',
        data: [320, 480, 380, 560],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4
      }
    ]
  },
  '90d': {
    labels: ['第1月', '第2月', '第3月'],
    datasets: [
      {
        label: '浏览量',
        data: [65000, 78000, 85000],
        borderColor: '#4f46e5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4
      },
      {
        label: '点赞数',
        data: [3200, 4100, 4800],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4
      },
      {
        label: '收藏数',
        data: [1200, 1580, 1890],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4
      }
    ]
  },
  '1y': {
    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
    datasets: [
      {
        label: '浏览量',
        data: [180000, 220000, 280000, 320000],
        borderColor: '#4f46e5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.4
      },
      {
        label: '点赞数',
        data: [8500, 11200, 14800, 18200],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        tension: 0.4
      },
      {
        label: '收藏数',
        data: [3200, 4500, 5800, 7200],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4
      }
    ]
  }
}

// 仪表板统计数据数组格式（用于其他组件）
export const dashboardStatsArray = [
  {
    key: 'views',
    label: '总浏览量',
    value: 125000,
    change: 12.5,
    icon: 'fas fa-eye',
    type: 'primary'
  },
  {
    key: 'likes',
    label: '总点赞数',
    value: 8900,
    change: 8.3,
    icon: 'fas fa-heart',
    type: 'success'
  },
  {
    key: 'comments',
    label: '总评论数',
    value: 2340,
    change: -2.1,
    icon: 'fas fa-comment',
    type: 'warning'
  },
  {
    key: 'followers',
    label: '关注者',
    value: 12800,
    change: 15.2,
    icon: 'fas fa-users',
    type: 'info'
  }
]

// 最近活动数据
export const recentActivities = [
  {
    id: 1,
    title: 'ChatGPT代码审查Prompt模板',
    description: '内容已发布并获得了156个点赞',
    type: 'published',
    status: 'published',
    time: '2025-01-22T10:30:00Z',
    icon: 'fas fa-check-circle'
  },
  {
    id: 2,
    title: 'AI Agent开发入门课程',
    description: '内容正在审核中，预计1-2个工作日完成',
    type: 'reviewing',
    status: 'reviewing',
    time: '2025-01-21T14:20:00Z',
    icon: 'fas fa-clock'
  },
  {
    id: 3,
    title: '用户@小明',
    description: '对您的文章《Vue 3 + AI 开发实战指南》发表了评论',
    type: 'comment',
    status: 'new',
    time: '2025-01-21T09:15:00Z',
    icon: 'fas fa-comment'
  }
]

// 系统通知数据
export const notifications = [
  {
    id: 1,
    title: '创作者激励计划',
    message: '恭喜您入选本月优秀创作者，将获得额外奖励！',
    type: 'success',
    read: false,
    time: '2025-01-22T08:00:00Z',
    icon: 'fas fa-trophy'
  },
  {
    id: 2,
    title: '内容审核通过',
    message: '您的MCP工具《智能客服工具包》已通过审核',
    type: 'info',
    read: false,
    time: '2025-01-21T16:30:00Z',
    icon: 'fas fa-check-circle'
  },
  {
    id: 3,
    title: '平台功能更新',
    message: '创作者中心新增数据分析功能，快来体验吧！',
    type: 'info',
    read: true,
    time: '2025-01-20T12:00:00Z',
    icon: 'fas fa-info-circle'
  }
]

// 团队数据
export const teams = [
  {
    id: 1,
    name: 'AI开发团队',
    description: '专注于AI应用开发的创作团队',
    memberCount: 5,
    teamRecommendationCount: 23,
    avatar: '/api/placeholder/60/60',
    role: 'owner'
  },
  {
    id: 2,
    name: '前端技术分享',
    description: '分享前端开发技术和经验',
    memberCount: 8,
    teamRecommendationCount: 45,
    avatar: '/api/placeholder/60/60',
    role: 'member'
  }
]

// 协作数据
export const collaborations = [
  {
    id: 1,
    title: 'AI应用开发系列教程',
    type: 'course',
    collaborators: ['张三', '李四', '王五'],
    status: 'in_progress',
    deadline: '2025-02-15',
    progress: 65
  },
  {
    id: 2,
    title: 'MCP工具开发指南',
    type: 'article',
    collaborators: ['赵六', '钱七'],
    status: 'review',
    deadline: '2025-01-30',
    progress: 90
  }
]

// 收益数据
export const revenueData = {
  totalRevenue: 15680,
  thisMonthRevenue: 3240,
  pendingRevenue: 890,
  availableForWithdraw: 12550,
  revenueByType: [
    { type: 'prompt', amount: 6800, percentage: 43 },
    { type: 'course', amount: 4200, percentage: 27 },
    { type: 'mcp', amount: 3100, percentage: 20 },
    { type: 'article', amount: 1580, percentage: 10 }
  ]
}

// 支付历史
export const paymentHistory = [
  {
    id: 1,
    amount: 2500,
    type: 'withdraw',
    status: 'completed',
    date: '2025-01-15',
    method: '支付宝'
  },
  {
    id: 2,
    amount: 1800,
    type: 'withdraw',
    status: 'pending',
    date: '2025-01-20',
    method: '银行卡'
  }
]

// 用户偏好设置
export const preferences = {
  defaultVisibility: 'public',
  autoSave: true,
  emailNotifications: true,
  pushNotifications: false,
  weeklyReport: true,
  contentRecommendations: true,
  theme: 'light',
  language: 'zh-CN'
}

// 快速统计数据
export const quickStats = {
  todayViews: 1250,
  todayLikes: 89,
  newFollowers: 23,
  viewsChange: 12.5,
  likesChange: 8.3,
  followersChange: 15.2
}

// 待办任务
export const upcomingTasks = [
  {
    id: 1,
    title: '完成AI Prompt教程第三章',
    deadline: '2025-01-25',
    priority: 'high',
    completed: false
  },
  {
    id: 2,
    title: '回复用户评论',
    deadline: '2025-01-23',
    priority: 'medium',
    completed: false
  },
  {
    id: 3,
    title: '更新MCP工具文档',
    deadline: '2025-01-26',
    priority: 'low',
    completed: false
  },
  {
    id: 4,
    title: '准备下周直播内容',
    deadline: '2025-01-28',
    priority: 'high',
    completed: false
  },
  {
    id: 5,
    title: '优化课程视频质量',
    deadline: '2025-01-30',
    priority: 'medium',
    completed: true
  }
]

// 热门话题
export const trendingTopics = [
  { id: 1, title: 'AI Agent开发', count: 1250, growth: 85 },
  { id: 2, title: 'Prompt工程', count: 980, growth: 62 },
  { id: 3, title: 'MCP协议', count: 756, growth: 45 },
  { id: 4, title: 'RAG应用', count: 642, growth: 38 },
  { id: 5, title: '多模态AI', count: 534, growth: 72 },
  { id: 6, title: 'AI安全', count: 423, growth: 28 },
  { id: 7, title: '边缘计算', count: 356, growth: 15 },
  { id: 8, title: '联邦学习', count: 289, growth: 22 }
]

// 分析数据
export const analyticsData = {
  coreMetrics: [
    {
      key: 'views',
      label: '总浏览量',
      value: 125000,
      trend: 12.5,
      change: 15600,
      icon: 'fas fa-eye',
      color: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
    },
    {
      key: 'likes',
      label: '总点赞数',
      value: 8900,
      trend: 8.3,
      change: 680,
      icon: 'fas fa-heart',
      color: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
    },
    {
      key: 'comments',
      label: '总评论数',
      value: 2340,
      trend: -2.1,
      change: -50,
      icon: 'fas fa-comment',
      color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'
    },
    {
      key: 'followers',
      label: '新增关注',
      value: 456,
      trend: 15.2,
      change: 60,
      icon: 'fas fa-users',
      color: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
    }
  ],
  userInteraction: {
    totalViews: 125000,
    totalLikes: 8900,
    totalComments: 2340,
    totalShares: 1560,
    avgViews: 2500,
    likeRate: 7.1,
    commentRate: 1.9,
    shareRate: 1.2
  }
}
