/**
 * 首页相关API服务
 */
import ApiClient from './api'

class HomeApi {
  /**
   * 获取首页统计数据
   */
  async getStats() {
    try {
      const response = await ApiClient.get('/portal/home/<USER>')
      return {
        success: true,
        data: response.data || {
          knowledge: '1,200+',
          solutions: '800+',
          users: '5,000+',
          satisfaction: '98%'
        }
      }
    } catch (error) {
      console.error('获取首页统计数据失败:', error)
      return {
        success: false,
        message: error.message || '获取统计数据失败',
        data: {
          knowledge: '1,200+',
          solutions: '800+',
          users: '5,000+',
          satisfaction: '98%'
        }
      }
    }
  }

  /**
   * 获取模块统计数据
   */
  async getModuleStats() {
    try {
      const response = await ApiClient.get('/portal/home/<USER>')
      return {
        success: true,
        data: response.data || {
          knowledge: {
            total: '1,200+',
            types: '8'
          },
          solutions: {
            total: '800+',
            categories: '12'
          },
          learning: {
            resources: '500+',
            courses: '50+'
          },
          recommendations: {
            accuracy: '95%',
            users: '3,000+'
          }
        }
      }
    } catch (error) {
      console.error('获取模块统计数据失败:', error)
      return {
        success: false,
        message: error.message || '获取模块统计数据失败',
        data: {
          knowledge: {
            total: '1,200+',
            types: '8'
          },
          solutions: {
            total: '800+',
            categories: '12'
          },
          learning: {
            resources: '500+',
            courses: '50+'
          },
          recommendations: {
            accuracy: '95%',
            users: '3,000+'
          }
        }
      }
    }
  }

  /**
   * 获取热门知识内容
   */
  async getFeaturedKnowledge(limit = 6) {
    try {
      const response = await ApiClient.get('/portal/knowledge/featured', {
        params: { limit }
      })
      return {
        success: true,
        data: response.data || []
      }
    } catch (error) {
      console.error('获取热门知识内容失败:', error)
      return {
        success: false,
        message: error.message || '获取热门知识内容失败',
        data: []
      }
    }
  }

  /**
   * 获取热门解决方案
   */
  async getFeaturedSolutions(limit = 6) {
    try {
      const response = await ApiClient.get('/portal/solutions/featured', {
        params: { limit }
      })
      return {
        success: true,
        data: response.data || []
      }
    } catch (error) {
      console.error('获取热门解决方案失败:', error)
      return {
        success: false,
        message: error.message || '获取热门解决方案失败',
        data: []
      }
    }
  }

  /**
   * 获取热门学习资源
   */
  async getFeaturedLearning(limit = 6) {
    try {
      const response = await ApiClient.get('/portal/learning/featured', {
        params: { limit }
      })
      return {
        success: true,
        data: response.data || []
      }
    } catch (error) {
      console.error('获取热门学习资源失败:', error)
      return {
        success: false,
        message: error.message || '获取热门学习资源失败',
        data: []
      }
    }
  }

  /**
   * 获取推荐内容
   */
  async getRecommendations(userId, limit = 10) {
    try {
      const response = await ApiClient.get('/portal/recommendations', {
        params: { userId, limit }
      })
      return {
        success: true,
        data: response.data || []
      }
    } catch (error) {
      console.error('获取推荐内容失败:', error)
      return {
        success: false,
        message: error.message || '获取推荐内容失败',
        data: []
      }
    }
  }

  /**
   * 记录用户行为（用于推荐系统）
   */
  async recordUserAction(action) {
    try {
      await ApiClient.post('/portal/user-actions', action)
      return { success: true }
    } catch (error) {
      console.error('记录用户行为失败:', error)
      return {
        success: false,
        message: error.message || '记录用户行为失败'
      }
    }
  }

  /**
   * 搜索内容
   */
  async search(query, filters = {}) {
    try {
      const response = await ApiClient.get('/portal/search', {
        params: {
          q: query,
          ...filters
        }
      })
      return {
        success: true,
        data: response.data || {
          knowledge: [],
          solutions: [],
          learning: [],
          total: 0
        }
      }
    } catch (error) {
      console.error('搜索失败:', error)
      return {
        success: false,
        message: error.message || '搜索失败',
        data: {
          knowledge: [],
          solutions: [],
          learning: [],
          total: 0
        }
      }
    }
  }
}

export default new HomeApi()
