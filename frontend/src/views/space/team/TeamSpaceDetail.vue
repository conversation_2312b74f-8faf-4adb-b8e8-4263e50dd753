<template>
  <Layout>
    <div class="team-space-detail">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>加载团队数据中...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">
          <i class="fas fa-exclamation-circle"></i>
        </div>
        <h3>加载失败</h3>
        <p>{{ error }}</p>
        <button class="btn btn-primary" @click="loadTeamData">重试</button>
      </div>
      
      <!-- 团队内容 -->
      <div v-else>
      <!-- 顶部导航栏 -->
      <header class="team-header">
        <div class="container">
          <nav class="header-nav">
            <div class="nav-left">
              <button class="back-button" @click="goBack">
                <i class="fas fa-arrow-left"></i>
                <span>返回</span>
              </button>
              
              <div class="breadcrumb">
                <span>团队空间</span>
                <i class="fas fa-chevron-right"></i>
                <div class="space-selector">
                  <button class="current-space" @click="toggleSpaceSelector">
                    {{ team.name }}
                    <i class="fas fa-chevron-down"></i>
                  </button>
                  
                  <!-- 空间切换下拉菜单 -->
                  <div class="space-dropdown" v-show="showSpaceSelector">
                    <div class="space-search">
                      <i class="fas fa-search"></i>
                      <input 
                        type="text" 
                        placeholder="搜索团队空间..."
                        v-model="spaceSearchQuery"
                      >
                    </div>
                    
                    <div class="space-list">
                      <div class="space-section">
                        <h4>我的团队</h4>
                        <div 
                          v-for="space in filteredMySpaces" 
                          :key="space.id"
                          class="space-item"
                          :class="{ active: space.id === team.id }"
                          @click="switchSpace(space)"
                        >
                          <img :src="space.avatar" :alt="space.name" class="space-avatar">
                          <div class="space-info">
                            <div class="space-name">{{ space.name }}</div>
                            <div class="space-meta">{{ space.membersCount }} 成员</div>
                          </div>
                          <div class="space-status" v-if="space.isActive">
                            <i class="fas fa-circle"></i>
                          </div>
                        </div>
                      </div>
                      
                      <div class="space-section" v-if="filteredOtherSpaces.length > 0">
                        <h4>其他空间</h4>
                        <div 
                          v-for="space in filteredOtherSpaces" 
                          :key="space.id"
                          class="space-item"
                          @click="switchSpace(space)"
                        >
                          <img :src="space.avatar" :alt="space.name" class="space-avatar">
                          <div class="space-info">
                            <div class="space-name">{{ space.name }}</div>
                            <div class="space-meta">{{ space.membersCount }} 成员</div>
                          </div>
                          <div class="space-actions">
                            <button class="join-btn" @click.stop="joinSpace(space)">
                              <i class="fas fa-plus"></i>
                            </button>
                          </div>
                        </div>
                        
                        <button class="show-more-spaces" @click="showAllSpaces" v-if="hasMoreSpaces">
                          <i class="fas fa-ellipsis-h"></i>
                          查看更多空间
                        </button>
                      </div>
                    </div>
                    
                    <div class="space-actions-footer">
                      <button class="create-space-btn" @click="createNewSpace">
                        <i class="fas fa-plus"></i>
                        创建新空间
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="nav-actions">
              <button class="action-btn" @click="toggleStar" :class="{ starred: team.isStarred }">
                <i :class="team.isStarred ? 'fas fa-star' : 'far fa-star'"></i>
              </button>
              <button class="action-btn" @click="shareTeam">
                <i class="fas fa-share-alt"></i>
              </button>
              <div class="dropdown" v-if="isAdmin">
                <button class="action-btn" @click="toggleDropdown">
                  <i class="fas fa-ellipsis-h"></i>
                </button>
                <div class="dropdown-menu" v-show="showDropdown">
                  <button @click="editTeam">编辑团队</button>
                </div>
              </div>
            </div>
          </nav>
        </div>
      </header>

      <!-- 团队英雄区域 -->
      <section class="team-hero">
        <div class="hero-background" :style="{ background: team.themeColor }">
          <div class="hero-pattern"></div>
        </div>
        
        <div class="container">
          <div class="hero-content">
            <div class="team-profile">
              <div class="team-avatar">
                <img v-if="team.avatar" :src="team.avatar" :alt="team.name">
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-users"></i>
                </div>
                <div class="status-indicator" v-if="team.isActive"></div>
              </div>
              
              <div class="team-info">
                <div class="team-badges">
                  <span class="badge" :class="team.isPublic ? 'public' : 'private'">
                    <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                    {{ team.isPublic ? '公开' : '私有' }}
                  </span>
                  <span class="badge verified" v-if="team.isVerified">
                    <i class="fas fa-shield-check"></i>
                    已认证
                  </span>
                </div>
                
                <h1 class="team-name">{{ team.name }}</h1>
                <p class="team-description">{{ team.description }}</p>
                
                <div class="team-meta">
                  <span class="meta-item">
                    <i class="fas fa-calendar-plus"></i>
                    创建于 {{ formatDate(team.createdAt) }}
                  </span>
                  <span class="meta-item" v-if="team.location">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ team.location }}
                  </span>
                </div>
                
                <div class="team-tags" v-if="team.tags?.length">
                  <span v-for="tag in team.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
              </div>
            </div>
            
            <div class="team-stats">
              <div class="stat-card" @click="scrollToMembers">
                <div class="stat-number">{{ team.membersCount }}</div>
                <div class="stat-label">成员</div>
              </div>
              <div class="stat-card" @click="showContent">
                <div class="stat-number">{{ team.contentCount }}</div>
                <div class="stat-label">内容</div>
              </div>
            </div>
            
            <div class="team-actions">
              <button 
                v-if="!team.isMember" 
                class="btn btn-primary" 
                @click="joinTeam"
                :disabled="joinLoading"
              >
                <i class="fas fa-plus"></i>
                {{ team.isPublic ? '加入团队' : '申请加入' }}
              </button>
              
              <button 
                v-else 
                class="btn btn-outline" 
                @click="inviteMembers"
              >
                <i class="fas fa-user-plus"></i>
                邀请成员
              </button>
              
            </div>
          </div>
        </div>
      </section>

      <!-- 主要内容区域 -->
      <main class="team-main">
        <div class="container">
          <div class="content-layout">
            <!-- 侧边栏 -->
            <aside class="sidebar">
              <!-- 在线成员 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-circle online-dot"></i>
                  在线成员 ({{ onlineMembers.length }})
                </h3>
                <div class="members-list">
                  <div
                    v-for="member in onlineMembers.slice(0, 6)"
                    :key="member.id"
                    class="member-item"
                  >
                    <img
                      :src="member.avatar"
                      :alt="member.name"
                      class="member-avatar clickable"
                      @click="viewMemberProfile(member)"
                    >
                    <div class="member-info">
                      <div
                        class="member-name clickable"
                        @click="viewMemberProfile(member)"
                      >{{ member.name }}</div>
                      <div class="member-status">{{ member.status || '在线' }}</div>
                    </div>
                  </div>
                  <button v-if="team.membersCount > 6" class="view-all" @click="setActiveTab('members')">
                    查看全部成员
                  </button>
                </div>
              </div>
              
              <!-- 成员贡献度 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-chart-line"></i>
                  成员贡献度
                </h3>
                <div class="contribution-list">
                  <div 
                    v-for="(member, index) in topContributors" 
                    :key="member.id" 
                    class="contribution-item"
                  >
                    <div class="rank-badge" :class="getRankClass(index)">
                      {{ index + 1 }}
                    </div>
                    <img 
                      :src="member.avatar" 
                      :alt="member.name" 
                      class="contributor-avatar clickable"
                      @click="viewMemberProfile(member)"
                    >
                    <div class="contributor-info">
                      <div 
                        class="contributor-name clickable"
                        @click="viewMemberProfile(member)"
                      >{{ member.name }}</div>
                      <div class="contribution-score">{{ member.contributionScore }} 分</div>
                    </div>
                    <div class="contribution-chart">
                      <div class="progress-bar">
                        <div 
                          class="progress-fill" 
                          :style="{ width: (member.contributionScore / maxContribution * 100) + '%' }"
                        ></div>
                      </div>
                    </div>
                  </div>
                  <button class="view-all" @click="showContributionDetails">
                    查看详细贡献
                  </button>
                </div>
              </div>
              
              <!-- 快速工具 - 已隐藏 -->
              <!--
              <div class="widget" v-if="team.isMember">
                <h3 class="widget-title">
                  <i class="fas fa-tools"></i>
                  快速工具
                </h3>
                <div class="tools-grid">
                  <button class="tool-btn" @click="openTool('calendar')">
                    <i class="fas fa-calendar"></i>
                    <span>日历</span>
                  </button>
                  <button class="tool-btn" @click="openTool('files')">
                    <i class="fas fa-folder"></i>
                    <span>文件</span>
                  </button>
                  <button class="tool-btn" @click="openTool('tasks')">
                    <i class="fas fa-tasks"></i>
                    <span>任务</span>
                  </button>
                  <button class="tool-btn" @click="openTool('notes')">
                    <i class="fas fa-sticky-note"></i>
                    <span>笔记</span>
                  </button>
                </div>
              </div>
              -->

              <!-- 团队动态 - 已隐藏 -->
              <!--
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-stream"></i>
                  团队动态
                </h3>
                <div class="activity-list">
                  <div
                    v-for="activity in recentActivities.slice(0, 5)"
                    :key="activity.id"
                    class="activity-item"
                  >
                    <div class="activity-icon">
                      <i :class="activity.icon"></i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-text">
                        <span
                          class="activity-user clickable"
                          @click="viewMemberProfile(activity.user)"
                        >{{ activity.user?.name }}</span>
                        {{ activity.action }}
                      </div>
                      <div class="activity-time">{{ formatTime(activity.time) }}</div>
                    </div>
                  </div>
                </div>
              </div>
              -->

              <!-- 热门内容 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-fire"></i>
                  热门内容
                </h3>
                <div class="popular-content-list">
                  <div
                    v-for="item in popularContent.slice(0, 4)"
                    :key="item.id"
                    class="popular-item"
                    @click="openContent(item)"
                  >
                    <div class="content-icon">
                      <i :class="getContentIcon(item.type)"></i>
                    </div>
                    <div class="content-info">
                      <div class="content-title">{{ item.title }}</div>
                      <div class="content-meta">
                        <span class="views">{{ item.views }} 浏览</span>
                        <span class="likes">{{ item.likes || 0 }} 点赞</span>
                      </div>
                    </div>
                  </div>
                  <button class="view-all" @click="setActiveTab('content')">
                    查看全部内容
                  </button>
                </div>
              </div>

              <!-- 团队成就 -->
              <div class="widget">
                <h3 class="widget-title">
                  <i class="fas fa-trophy"></i>
                  团队成就
                </h3>
                <div class="achievements-compact">
                  <div
                    v-for="achievement in teamAchievements.slice(0, 3)"
                    :key="achievement.id"
                    class="achievement-compact-item"
                  >
                    <div class="achievement-icon">
                      <i :class="achievement.icon"></i>
                    </div>
                    <div class="achievement-text">
                      <div class="achievement-title">{{ achievement.title }}</div>
                      <div class="achievement-desc">{{ achievement.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </aside>
            
            <!-- 主内容区 -->
            <div class="main-content">
              <!-- 标签导航 -->
              <nav class="content-nav">
                <div class="nav-tabs">
                  <button 
                    v-for="tab in tabs" 
                    :key="tab.key"
                    class="nav-tab"
                    :class="{ active: activeTab === tab.key }"
                    @click="setActiveTab(tab.key)"
                  >
                    <i :class="tab.icon"></i>
                    <span>{{ tab.label }}</span>
                    <span v-if="tab.count" class="tab-count">{{ tab.count }}</span>
                  </button>
                </div>
                
                <div class="nav-actions">
                  <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input
                      type="text"
                      placeholder="搜索..."
                      v-model="searchQuery"
                    >
                  </div>
                </div>
              </nav>
              
              <!-- 标签内容 -->
              <div class="tab-content">
                <!-- 内容管理 -->
                <div v-if="activeTab === 'content'" class="tab-panel">
                  <TeamSpaceContent
                    v-if="team.id && team.id !== 0"
                    :team-id="team.id"
                    :is-member="team.isMember"
                  />
                  <div v-else class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>正在加载团队信息...</p>
                  </div>
                </div>



                <!-- 成员管理 -->
                <div v-else-if="activeTab === 'members'" class="tab-panel">
                  <div class="members-management">
                    <!-- 成员统计概览 -->
                    <div class="members-overview">
                      <div class="overview-stats">
                        <div class="stat-item">
                          <div class="stat-number">{{ team.membersCount }}</div>
                          <div class="stat-label">总成员</div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-number">{{ onlineMembers.length }}</div>
                          <div class="stat-label">在线成员</div>
                        </div>
                        <div class="stat-item">
                          <div class="stat-number">{{ adminCount }}</div>
                          <div class="stat-label">管理团队</div>
                        </div>
                      </div>

                      <div class="members-actions">
                        <button
                          class="btn btn-outline"
                          @click="exportMemberList"
                          :disabled="exportLoading"
                        >
                          <i class="fas fa-spinner fa-spin" v-if="exportLoading"></i>
                          <i class="fas fa-download" v-else></i>
                          {{ exportLoading ? '导出中...' : '导出列表' }}
                        </button>
                      </div>
                    </div>

                    <!-- 成员过滤和搜索 -->
                    <div class="members-toolbar">
                      <div class="filter-section">
                        <div class="filter-buttons">
                          <button
                            v-for="filter in memberFilters"
                            :key="filter.key"
                            class="filter-btn"
                            :class="{ active: activeMemberFilter === filter.key }"
                            @click="setMemberFilter(filter.key)"
                          >
                            <i :class="filter.icon"></i>
                            <span class="filter-label">{{ filter.label }}</span>
                            <span class="filter-count" v-if="filter.count">{{ filter.count }}</span>
                          </button>
                        </div>
                      </div>

                      <div class="search-section">
                        <div class="search-members">
                          <i class="fas fa-search"></i>
                          <input
                            type="text"
                            placeholder="搜索成员姓名或邮箱..."
                            v-model="memberSearchQuery"
                          >
                          <button
                            v-if="memberSearchQuery"
                            class="clear-search"
                            @click="memberSearchQuery = ''"
                          >
                            <i class="fas fa-times"></i>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- 成员网格 -->
                    <div class="members-grid">
                      <div
                        v-for="member in filteredMembersForTab"
                        :key="member.id"
                        class="member-card"
                        @click="viewMemberProfile(member)"
                      >
                        <div class="member-header">
                          <img
                            :src="member.avatar"
                            :alt="member.name"
                            class="member-avatar"
                          >
                          <div class="member-status" :class="{ online: member.isOnline }">
                            <i class="fas fa-circle"></i>
                          </div>
                          <div class="member-role-badge" :class="member.role">
                            {{ getRoleLabel(member.role) }}
                          </div>
                        </div>

                        <div class="member-info">
                          <h3 class="member-name">{{ member.name }}</h3>
                          <p class="member-email">{{ member.email }}</p>

                          <div class="member-contribution">
                            <div class="contribution-label">贡献度</div>
                            <div class="contribution-bar">
                              <div
                                class="contribution-fill"
                                :style="{ width: (member.contributionScore / maxContribution * 100) + '%' }"
                              ></div>
                            </div>
                            <div class="contribution-score">{{ member.contributionScore }} 分</div>
                          </div>

                          <div class="member-meta">
                            <div class="join-date">
                              <i class="fas fa-calendar"></i>
                              {{ formatDate(member.joinedAt) }} 加入
                            </div>
                          </div>
                        </div>

                        <div class="member-actions">

                          <button
                            class="action-btn"
                            @click.stop="editMemberRole(member)"
                            title="编辑角色"
                            v-if="canManageMember(member)"
                          >
                            <i class="fas fa-user-cog"></i>
                          </button>


                          <button
                            class="action-btn danger"
                            @click.stop="removeMember(member)"
                            title="移出团队"
                            v-if="canRemoveMember(member)"
                          >
                            <i class="fas fa-user-minus"></i>
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- 空状态 -->
                    <div v-if="filteredMembersForTab.length === 0" class="empty-state">
                      <i class="fas fa-users"></i>
                      <h3>暂无成员</h3>
                      <p>没有找到符合条件的成员</p>
                    </div>
                  </div>
                </div>

                <!-- 数据洞察 -->
                <div v-else-if="activeTab === 'analytics'" class="tab-panel">
                  <div class="analytics-dashboard">
                    <!-- 核心指标 -->
                    <div class="metrics-grid">
                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>团队活跃度</h3>
                          <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="metric-value">85%</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +12% 较上月
                        </div>
                      </div>

                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>内容产出</h3>
                          <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="metric-value">156</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +23 本月
                        </div>
                      </div>

                      <div class="metric-card">
                        <div class="metric-header">
                          <h3>协作效率</h3>
                          <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-value">92%</div>
                        <div class="metric-trend positive">
                          <i class="fas fa-arrow-up"></i>
                          +8% 较上月
                        </div>
                      </div>


                    </div>

                    <!-- 详细分析 -->
                    <div class="analytics-details">
                      <div class="analytics-section">
                        <h3>成员贡献分析</h3>
                        <div class="contribution-chart">
                          <div class="chart-placeholder">
                            <i class="fas fa-chart-bar"></i>
                            <p>贡献度分布图表</p>
                          </div>
                        </div>
                      </div>

                      <div class="analytics-section">
                        <h3>内容类型分布</h3>
                        <div class="content-distribution">
                          <div class="distribution-item">
                            <div class="distribution-label">文章</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 45%"></div>
                            </div>
                            <div class="distribution-value">45%</div>
                          </div>
                          <div class="distribution-item">
                            <div class="distribution-label">工具</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 30%"></div>
                            </div>
                            <div class="distribution-value">30%</div>
                          </div>
                          <div class="distribution-item">
                            <div class="distribution-label">设计</div>
                            <div class="distribution-bar">
                              <div class="distribution-fill" style="width: 25%"></div>
                            </div>
                            <div class="distribution-value">25%</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他标签内容 -->
                <div v-else class="tab-panel">
                  <div class="content-placeholder">
                    <i class="fas fa-construction"></i>
                    <h3>功能开发中</h3>
                    <p>{{ activeTab }} 功能正在开发中，敬请期待</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <!-- 邀请成员模态框 -->
      <div class="modal-overlay" v-show="showInviteModal" @click="closeInviteModal">
        <div class="modal-content invite-member-modal" @click.stop>
          <div class="modal-header">
            <h2>邀请成员</h2>
            <button class="close-btn" @click="closeInviteModal">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <div class="invite-form">
              <div class="form-group">
                <label for="userSearch">搜索用户</label>
                <div class="user-search-container">
                  <div class="search-input-wrapper">
                    <i class="fas fa-search"></i>
                    <input
                      type="text"
                      id="userSearch"
                      v-model="userSearchQuery"
                      @input="searchUsers"
                      placeholder="输入用户名、邮箱或姓名..."
                      autocomplete="off"
                    >
                    <div class="search-loading" v-if="searchLoading">
                      <i class="fas fa-spinner fa-spin"></i>
                    </div>
                  </div>

                  <!-- 搜索结果下拉 -->
                  <div class="search-results" v-if="searchResults.length > 0 || (userSearchQuery && !searchLoading)">
                    <div
                      v-for="user in searchResults"
                      :key="user.id"
                      class="search-result-item"
                      @click="selectUser(user)"
                    >
                      <img :src="user.avatar" :alt="user.name" class="result-avatar">
                      <div class="result-info">
                        <div class="result-header">
                          <div class="result-name">{{ user.name }}</div>
                          <div class="result-status" v-if="user.isOnline">
                            <i class="fas fa-circle online"></i>
                            <span>在线</span>
                          </div>
                        </div>
                        <div class="result-email">{{ user.email }}</div>
                        <div class="result-meta">
                          <span v-if="user.title" class="user-title">{{ user.title }}</span>
                          <span v-if="user.department" class="user-department">{{ user.department }}</span>
                        </div>
                        <div class="result-tags" v-if="user.tags && user.tags.length > 0">
                          <span v-for="tag in user.tags.slice(0, 3)" :key="tag" class="user-tag">{{ tag }}</span>
                        </div>
                        <div class="result-bio" v-if="user.bio">{{ user.bio }}</div>
                      </div>
                    </div>

                    <div v-if="searchResults.length === 0 && userSearchQuery && !searchLoading" class="no-results">
                      <i class="fas fa-user-slash"></i>
                      <span>未找到匹配的用户</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 已选择的用户 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label>已选择的用户 ({{ selectedUsers.length }})</label>
                <div class="selected-users">
                  <div
                    v-for="user in selectedUsers"
                    :key="user.id"
                    class="selected-user-item"
                  >
                    <img :src="user.avatar" :alt="user.name" class="selected-avatar">
                    <div class="selected-info">
                      <div class="selected-name">{{ user.name }}</div>
                      <div class="selected-email">{{ user.email }}</div>
                    </div>
                    <button class="remove-user-btn" @click="removeSelectedUser(user)">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- 邀请角色选择 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label>邀请角色</label>
                <div class="role-selector">
                  <label class="role-option">
                    <input type="radio" v-model="inviteRole" value="member">
                    <span class="role-label">
                      <i class="fas fa-user"></i>
                      <div class="role-info">
                        <div class="role-title">成员</div>
                        <div class="role-desc">可以查看和参与团队内容</div>
                      </div>
                    </span>
                  </label>
                  <label class="role-option">
                    <input type="radio" v-model="inviteRole" value="admin">
                    <span class="role-label">
                      <i class="fas fa-user-shield"></i>
                      <div class="role-info">
                        <div class="role-title">管理员</div>
                        <div class="role-desc">可以管理团队成员和设置</div>
                      </div>
                    </span>
                  </label>
                </div>
              </div>

              <!-- 邀请消息 -->
              <div class="form-group" v-if="selectedUsers.length > 0">
                <label for="inviteMessage">邀请消息 (可选)</label>
                <textarea
                  id="inviteMessage"
                  v-model="inviteMessage"
                  placeholder="添加一些个人消息，让邀请更有温度..."
                  rows="3"
                  maxlength="200"
                ></textarea>
                <div class="char-count">{{ inviteMessage.length }}/200</div>
              </div>

              <div class="form-actions">
                <button type="button" class="btn btn-outline" @click="closeInviteModal">
                  取消
                </button>
                <button
                  type="button"
                  class="btn btn-primary"
                  @click="sendInvitations"
                  :disabled="selectedUsers.length === 0 || inviteLoading"
                >
                  <i class="fas fa-paper-plane" v-if="!inviteLoading"></i>
                  <i class="fas fa-spinner fa-spin" v-else></i>
                  {{ inviteLoading ? '发送中...' : `邀请 ${selectedUsers.length} 位用户` }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 日历模态框 -->
      <div class="modal-overlay" v-show="showCalendarModal" @click="closeCalendar">
        <div class="modal-content calendar-modal" @click.stop>
          <div class="modal-header">
            <h2>团队日历</h2>
            <button class="close-btn" @click="closeCalendar">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <div class="modal-body">
            <div class="calendar-container">
              <!-- 日历头部 -->
              <div class="calendar-header">
                <button class="nav-btn" @click="previousMonth">
                  <i class="fas fa-chevron-left"></i>
                </button>
                <h3 class="calendar-title">
                  {{ formatCalendarMonth(currentDate) }}
                </h3>
                <button class="nav-btn" @click="nextMonth">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>

              <!-- 日历网格 -->
              <div class="calendar-grid">
                <!-- 星期标题 -->
                <div class="weekday-header">
                  <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
                </div>

                <!-- 日期网格 -->
                <div class="dates-grid">
                  <div
                    v-for="date in calendarDates"
                    :key="date.key"
                    class="date-cell"
                    :class="{
                      'other-month': !date.isCurrentMonth,
                      'today': date.isToday,
                      'selected': date.isSelected,
                      'has-events': date.events.length > 0
                    }"
                    @click="selectDate(date.date)"
                  >
                    <div class="date-number">{{ date.day }}</div>
                    <div class="date-events" v-if="date.events.length > 0">
                      <div
                        v-for="event in date.events.slice(0, 2)"
                        :key="event.id"
                        class="event-dot"
                        :class="event.type"
                        :title="event.title"
                      ></div>
                      <div v-if="date.events.length > 2" class="more-events">
                        +{{ date.events.length - 2 }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 选中日期的事件详情 -->
              <div class="event-details" v-if="selectedDate">
                <h4>{{ formatSelectedDate(selectedDate) }} 的事项</h4>
                <div class="events-list">
                  <div
                    v-for="event in getEventsForDate(selectedDate)"
                    :key="event.id"
                    class="event-item"
                    :class="event.type"
                  >
                    <div class="event-time">{{ event.time }}</div>
                    <div class="event-content">
                      <div class="event-title">{{ event.title }}</div>
                      <div class="event-participants" v-if="event.participants?.length">
                        <i class="fas fa-users"></i>
                        {{ event.participants.join(', ') }}
                      </div>
                    </div>
                    <div class="event-type-icon">
                      <i :class="getEventIcon(event.type)"></i>
                    </div>
                  </div>

                  <div v-if="getEventsForDate(selectedDate).length === 0" class="no-events">
                    <i class="fas fa-calendar-day"></i>
                    <span>这一天没有安排事项</span>
                  </div>
                </div>

                <div class="event-actions">
                  <button class="btn btn-primary btn-sm" @click="createEvent">
                    <i class="fas fa-plus"></i>
                    添加事项
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
    </div>

    <!-- 编辑成员角色模态框 -->
    <EditMemberRoleModal
      :show="showEditRoleModal"
      :member="editingMember"
      :team-id="team.id"
      @update:show="closeEditRoleModal"
      @role-updated="handleMemberRoleUpdated"
    />

    <!-- 编辑团队信息模态框 -->
    <EditTeamModal
      v-if="team && team.id"
      :visible="showEditTeamModal"
      :team="team"
      @close="closeEditTeamModal"
      @updated="handleTeamUpdated"
    />
  </Layout>
</template>

<script>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Layout from '../../../components/Layout.vue'
import EditMemberRoleModal from './components/EditMemberRoleModal.vue'
import EditTeamModal from './components/EditTeamModal.vue'
import TeamSpaceContent from './components/TeamSpaceContent.vue'
import { useToastStore } from '../../../stores/toast'
import { useUserStore } from '../../../stores/user'
import userService from '../../../services/userService'
import teamService from '../../../services/teamService'
import { getRoleLabel, getRoleIcon } from '../../../utils/roleUtils'

export default {
  name: 'TeamSpaceDetail',
  components: {
    Layout,
    EditMemberRoleModal,
    EditTeamModal,
    TeamSpaceContent
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const toastStore = useToastStore()
    const userStore = useUserStore()
    
    // 引用TeamSpaceContent组件

    // 加载团队数据
    
    // 响应式数据
    const joinLoading = ref(false)
    const showDropdown = ref(false)
    const showSpaceSelector = ref(false)
    const showMemberModal = ref(false)
    const showMemberActions = ref(false)

    const showInviteModal = ref(false)
    const showEditRoleModal = ref(false)
    const showEditTeamModal = ref(false)
    const showCalendarModal = ref(false)
    const editingMember = ref(null)
    const activeTab = ref('content')

    const activeContentFilter = ref('all')
    const activeMemberFilter = ref('all')
    const contentView = ref('grid')
    const searchQuery = ref('')
    const spaceSearchQuery = ref('')
    const memberSearchQuery = ref('')

    const userSearchQuery = ref('')

    // 邀请成员相关数据
    const searchLoading = ref(false)
    const inviteLoading = ref(false)
    const exportLoading = ref(false)
    const searchResults = ref([])
    const selectedUsers = ref([])
    const inviteRole = ref('member')
    const inviteMessage = ref('')

    // 日历相关数据
    const currentDate = ref(new Date())
    const selectedDate = ref(null)
    const calendarEvents = ref([])



    // 团队数据
    const team = reactive({
      id: 0,
      name: '',
      description: '',
      avatar: '',
      themeColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      isPublic: true,
      isMember: false,
      isStarred: false,
      isVerified: false,
      isActive: true,
      location: '',
      createdAt: '',
      membersCount: 0,
      contentCount: 0,
      tags: []
    })
    
    // 加载状态
    const loading = ref(false)
    const error = ref(null)

    // 团队空间列表 - 从API获取
    const mySpaces = ref([])
    const otherSpaces = ref([])

    // 加载团队列表数据
    const loadTeamSpaces = async () => {
      try {
        const teamsData = await teamService.getAllTeams({ page: 1, pageSize: 20 })
        if (teamsData && teamsData.list) {
          // 这里可以根据实际需求分类团队，比如用户加入的团队和其他团队
          // 暂时将所有团队放到otherSpaces中
          otherSpaces.value = teamsData.list.map(team => ({
            id: team.teamId || team.id,
            name: team.name,
            avatar: team.avatarUrl || `https://api.dicebear.com/7.x/shapes/svg?seed=${team.name}`,
            membersCount: team.memberCount || 0,
            isActive: team.isActive !== false
          }))
        }
      } catch (error) {
        console.error('加载团队列表失败:', error)
      }
    }

    // 在线成员（从所有成员中筛选）
    const onlineMembers = computed(() => {
      return allMembers.value.filter(member => member.isOnline).map(member => ({
        id: member.id,
        name: member.name,
        avatar: member.avatar,
        status: '在线',
        isOnline: member.isOnline
      }))
    })

    // 所有成员列表（用于成员管理）
    const allMembers = ref([])

    // 贡献度排行
    const topContributors = computed(() => {
      return [...allMembers.value]
        .sort((a, b) => b.contributionScore - a.contributionScore)
        .slice(0, 5)
    })

    const maxContribution = computed(() => {
      return Math.max(...allMembers.value.map(m => m.contributionScore))
    })

    // 最近活动
    const recentActivities = ref([
      {
        id: 1,
        icon: 'fas fa-file-alt',
        user: { id: 1, name: '张三' },
        action: '发布了新文章《深度学习在图像识别中的应用》',
        time: '2024-01-20T14:30:00Z'
      },
      {
        id: 2,
        icon: 'fas fa-user-plus',
        user: { id: 2, name: '李四' },
        action: '加入了团队',
        time: '2024-01-20T10:15:00Z'
      }
    ])

    // 标签页配置（移除概览，专注核心功能）
    const tabs = computed(() => [
      { key: 'content', label: '内容', icon: 'fas fa-file-alt', count: team.contentCount },
      { key: 'members', label: '成员', icon: 'fas fa-users', count: team.membersCount },
      { key: 'analytics', label: '数据洞察', icon: 'fas fa-chart-bar' }
    ])

    // 概览数据
    const teamDynamics = ref([
      { id: 1, text: '张三发布了新文章', time: '2024-01-20T14:30:00Z' },
      { id: 3, text: '新成员加入团队', time: '2024-01-20T10:30:00Z' }
    ])

    const popularContent = ref([
      {
        id: 1,
        type: 'article',
        title: '深度学习最佳实践',
        views: 1250,
        likes: 89,
        description: '详细介绍深度学习在实际项目中的最佳实践方法和技巧',
        author: { id: 1, name: '张三', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhang' },
        thumbnail: null,
        tags: ['深度学习', 'AI', '最佳实践'],
        comments: 23,
        updatedAt: '2024-01-20T14:30:00Z'
      },
      {
        id: 2,
        type: 'tool',
        title: 'AI模型训练工具',
        views: 890,
        likes: 67,
        description: '高效的AI模型训练工具，支持多种深度学习框架',
        author: { id: 2, name: '李四', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=li' },
        thumbnail: null,
        tags: ['工具', 'AI', '训练'],
        comments: 15,
        updatedAt: '2024-01-19T16:45:00Z'
      },
      {
        id: 3,
        type: 'design',
        title: 'UI设计规范',
        views: 567,
        likes: 45,
        description: '团队UI设计规范文档，包含组件库和设计原则',
        author: { id: 3, name: '王五', avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wang' },
        thumbnail: null,
        tags: ['设计', 'UI', '规范'],
        comments: 8,
        updatedAt: '2024-01-18T11:20:00Z'
      }
    ])

    // 内容过滤器
    const contentFilters = ref([
      { key: 'all', label: '全部内容', icon: 'fas fa-file-alt', count: 156 },
      { key: 'article', label: '文章', icon: 'fas fa-newspaper', count: 89 },
      { key: 'tool', label: '工具', icon: 'fas fa-tools', count: 34 },
      { key: 'design', label: '设计', icon: 'fas fa-palette', count: 23 },
      { key: 'video', label: '视频', icon: 'fas fa-video', count: 10 }
    ])

    // 成员过滤器
    const memberFilters = computed(() => [
      { key: 'all', label: '全部成员', icon: 'fas fa-users', count: allMembers.value.length },
      { key: 'online', label: '在线成员', icon: 'fas fa-circle', count: onlineMembers.value.length },
      { key: 'admin', label: '管理团队', icon: 'fas fa-user-shield', count: adminCount.value }
    ])

    const teamAchievements = ref([
      {
        id: 1,
        icon: 'fas fa-trophy',
        title: '创新团队',
        description: '发布了10+创新项目'
      },
      {
        id: 2,
        icon: 'fas fa-star',
        title: '知识分享',
        description: '团队文章获得1000+点赞'
      },
      {
        id: 3,
        icon: 'fas fa-users',
        title: '团队协作',
        description: '成员协作效率提升50%'
      }
    ])



    // 计算属性
    const isAdmin = computed(() => {
      // 检查用户是否为管理员或创建者
      // userRole: 0=成员, 1=管理员, 2=创建者
      return team.isMember && team.userRole >= 1
    })

    const isCreator = computed(() => {
      return team.isMember && team.userRole === 2
    })

    const canCreate = computed(() => {
      return activeTab.value !== 'overview'
    })

    const filteredMySpaces = computed(() => {
      if (!spaceSearchQuery.value) return mySpaces.value
      return mySpaces.value.filter(space =>
        space.name.toLowerCase().includes(spaceSearchQuery.value.toLowerCase())
      )
    })

    const filteredOtherSpaces = computed(() => {
      if (!spaceSearchQuery.value) return otherSpaces.value
      return otherSpaces.value.filter(space =>
        space.name.toLowerCase().includes(spaceSearchQuery.value.toLowerCase())
      )
    })

    const hasMoreSpaces = computed(() => {
      return otherSpaces.value.length > 5
    })

    const filteredMembers = computed(() => {
      if (!memberSearchQuery.value) return allMembers.value
      return allMembers.value.filter(member =>
        member.name.toLowerCase().includes(memberSearchQuery.value.toLowerCase()) ||
        member.email.toLowerCase().includes(memberSearchQuery.value.toLowerCase())
      )
    })



    const filteredContent = computed(() => {
      let filtered = popularContent.value

      if (activeContentFilter.value !== 'all') {
        filtered = filtered.filter(content => content.type === activeContentFilter.value)
      }

      return filtered
    })

    const filteredMembersForTab = computed(() => {
      let filtered = allMembers.value

      // 按过滤器筛选
      switch (activeMemberFilter.value) {
        case 'online':
          filtered = filtered.filter(member => member.isOnline)
          break
        case 'admin':
          // 将创建者和管理员都归类到管理团队
          filtered = filtered.filter(member =>
            member.role === 'admin' ||
            member.role === 'owner' ||
            member.role === 'creator'
          )
          break
        default:
          filtered = filtered
      }

      // 按搜索词筛选
      if (memberSearchQuery.value) {
        filtered = filtered.filter(member =>
          member.name.toLowerCase().includes(memberSearchQuery.value.toLowerCase()) ||
          member.email.toLowerCase().includes(memberSearchQuery.value.toLowerCase()) ||
          (member.department && member.department.toLowerCase().includes(memberSearchQuery.value.toLowerCase()))
        )
      }

      return filtered
    })

    const adminCount = computed(() => {
      // 统计管理员、创建者和所有者的总数
      return allMembers.value.filter(member =>
        member.role === 'admin' ||
        member.role === 'owner' ||
        member.role === 'creator'
      ).length
    })



    // 日历计算属性
    const weekdays = ['日', '一', '二', '三', '四', '五', '六']

    const calendarDates = computed(() => {
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth()

      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1)
      const lastDay = new Date(year, month + 1, 0)

      // 获取第一周的开始日期（可能是上个月的日期）
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())

      // 获取最后一周的结束日期（可能是下个月的日期）
      const endDate = new Date(lastDay)
      endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()))

      const dates = []
      const current = new Date(startDate)
      const today = new Date()

      while (current <= endDate) {
        const date = new Date(current)
        const events = getEventsForDate(date)

        dates.push({
          key: date.toISOString().split('T')[0],
          date: date,
          day: date.getDate(),
          isCurrentMonth: date.getMonth() === month,
          isToday: date.toDateString() === today.toDateString(),
          isSelected: selectedDate.value && date.toDateString() === selectedDate.value.toDateString(),
          events: events
        })

        current.setDate(current.getDate() + 1)
      }

      return dates
    })

    // 方法
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatTime = (dateString) => {
      const now = new Date()
      const time = new Date(dateString)
      const diff = now - time

      const minutes = Math.floor(diff / 60000)
      const hours = Math.floor(diff / 3600000)
      const days = Math.floor(diff / 86400000)

      if (days > 0) return `${days}天前`
      if (hours > 0) return `${hours}小时前`
      if (minutes > 0) return `${minutes}分钟前`
      return '刚刚'
    }

    const getContentIcon = (type) => {
      const icons = {
        article: 'fas fa-file-alt',
        tool: 'fas fa-tools',
        design: 'fas fa-palette',
        video: 'fas fa-video'
      }
      return icons[type] || 'fas fa-file'
    }

    const getRankClass = (index) => {
      if (index === 0) return 'gold'
      if (index === 1) return 'silver'
      if (index === 2) return 'bronze'
      return 'normal'
    }



    // 事件处理
    const goBack = () => {
      router.go(-1)
    }

    const toggleStar = () => {
      team.isStarred = !team.isStarred
      toastStore.success(team.isStarred ? '已收藏团队' : '已取消收藏')
    }

    const shareTeam = () => {
      if (navigator.share) {
        navigator.share({
          title: `${team.name} - 团队空间`,
          url: window.location.href
        })
      } else {
        navigator.clipboard.writeText(window.location.href)
        toastStore.success('链接已复制到剪贴板')
      }
    }

    const toggleDropdown = () => {
      showDropdown.value = !showDropdown.value
    }

    const toggleSpaceSelector = () => {
      showSpaceSelector.value = !showSpaceSelector.value
    }

    const switchSpace = (space) => {
      if (space.id !== team.id) {
        router.push(`/team-space/${space.id}`)
      }
      showSpaceSelector.value = false
    }

    const joinSpace = (space) => {
      toastStore.info(`申请加入 ${space.name}`)
    }

    const showAllSpaces = () => {
      toastStore.info('显示所有团队空间功能开发中...')
    }

    const createNewSpace = () => {
      router.push('/team-space/create')
    }

    // 修复用户跳转路径
    const viewMemberProfile = (member) => {
      if (member && member.id) {
        // 获取当前登录用户ID
        const currentUserId = userStore.currentUserId || userStore.user?.id

        // 判断是否是登录者自己
        if (member.id === currentUserId || member.userId === currentUserId) {
          // 跳转到个人主页
          router.push('/profile')
        } else {
          // 跳转到他人用户页面
          router.push(`/user/${member.id}`)
        }
      }
    }

    const joinTeam = async () => {
      joinLoading.value = true
      try {
        await new Promise(resolve => setTimeout(resolve, 1000))
        team.isMember = true
        team.membersCount++
        toastStore.success('成功加入团队')
      } catch (error) {
        toastStore.error('加入团队失败，请重试')
      } finally {
        joinLoading.value = false
      }
    }

    const setActiveTab = (tab) => {
      activeTab.value = tab
      // 默认显示内容标签页
      if (!tab) {
        activeTab.value = 'content'
      }
    }



    const closeMemberModal = () => {
      showMemberModal.value = false
    }

    const toggleMemberActions = () => {
      showMemberActions.value = !showMemberActions.value
    }

    // 角色标签转换函数
    const getRoleLabel = (role) => {
      switch (role) {
        case 'admin': return '管理员'
        case 'member': return '成员'
        case 'owner':
        case 'creator': return '创建者'
        default: return '成员'
      }
    }

    const canManageMember = (member) => {
      // 只有管理员和创建者才能管理成员
      if (!isAdmin.value) {
        return false
      }

      // 不能操作自己的角色
      const currentUserId = userStore.currentUserId
      if (member.id === currentUserId || member.userId === currentUserId) {
        return false
      }

      // 获取成员的角色值（支持数字和字符串格式）
      let memberRoleValue = member.roleValue
      if (memberRoleValue === undefined) {
        // 如果没有数字角色值，从字符串角色转换
        if (member.role === 'owner' || member.role === 'creator') {
          memberRoleValue = 2
        } else if (member.role === 'admin') {
          memberRoleValue = 1
        } else {
          memberRoleValue = 0
        }
      }

      // 不能操作创建者的角色 (roleValue = 2)
      if (memberRoleValue === 2) {
        return false
      }

      // 普通管理员不能操作其他管理员的角色，只有创建者可以
      if (memberRoleValue === 1 && !isCreator.value) {
        return false
      }

      return true
    }

    const canRemoveMember = (member) => {
      // 只有管理员和创建者才能移除成员
      if (!isAdmin.value) return false

      // 不能移除自己
      const currentUserId = userStore.currentUserId
      if (member.id === currentUserId || member.userId === currentUserId) return false

      // 获取成员的角色值（支持数字和字符串格式）
      let memberRoleValue = member.roleValue
      if (memberRoleValue === undefined) {
        // 如果没有数字角色值，从字符串角色转换
        if (member.role === 'owner' || member.role === 'creator') {
          memberRoleValue = 2
        } else if (member.role === 'admin') {
          memberRoleValue = 1
        } else {
          memberRoleValue = 0
        }
      }

      // 不能移除创建者 (roleValue = 2)
      if (memberRoleValue === 2) return false

      // 普通管理员不能移除其他管理员，只有创建者可以
      if (memberRoleValue === 1 && !isCreator.value) return false

      return true
    }

    const updateMemberRole = (member) => {
      toastStore.success(`已更新 ${member.name} 的角色为 ${member.role}`)
    }

    const removeMember = async (member) => {
      if (confirm(`确定要将 ${member.name} 移出团队吗？`)) {
        try {
          const currentUserId = userStore.currentUserId
          await teamService.removeTeamMember(team.id, member.id, currentUserId)

          // 从本地列表中移除
          const index = allMembers.value.findIndex(m => m.id === member.id)
          if (index > -1) {
            allMembers.value.splice(index, 1)
            team.membersCount--
          }

          toastStore.success(`已将 ${member.name} 移出团队`)
        } catch (error) {
          console.error('移除成员失败:', error)
          toastStore.error('移除成员失败: ' + (error.message || '未知错误'))
        }
      }
    }

    const exportMemberList = async () => {
      if (exportLoading.value) return

      exportLoading.value = true

      try {
        // 检查是否有成员数据
        if (!allMembers.value || allMembers.value.length === 0) {
          toastStore.warning('暂无成员数据可导出')
          return
        }

        // 添加一个小延迟，让用户看到加载状态
        await new Promise(resolve => setTimeout(resolve, 300))

        // 准备导出数据
        const exportData = allMembers.value.map((member, index) => ({
          '序号': index + 1,
          '姓名': member.name || '未知',
          '邮箱': member.email || '未填写',
          '角色': getRoleLabel(member.role),
          '加入时间': formatDate(member.joinedAt),
          '贡献度': member.contributionScore || 0,
          '在线状态': member.isOnline ? '在线' : '离线',
          '发布文章': member.achievements?.articlesPublished || 0,
          '总浏览量': member.achievements?.totalViews || 0,
          '总点赞': member.achievements?.totalLikes || 0,
          '总收藏': member.achievements?.totalFavorites || 0
        }))

        // 创建 CSV 内容
        const headers = Object.keys(exportData[0])
        const csvContent = [
          headers.join(','),
          ...exportData.map(row =>
            headers.map(header => {
              const value = row[header]
              // 处理包含逗号或换行符的值
              if (typeof value === 'string' && (value.includes(',') || value.includes('\n') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`
              }
              return value
            }).join(',')
          )
        ].join('\n')

        // 创建 Blob 对象
        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })

        // 创建下载链接
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)

        // 生成文件名（移除特殊字符）
        const teamName = (team.name || '团队').replace(/[<>:"/\\|?*]/g, '_')
        const timestamp = new Date().toISOString().slice(0, 10)
        const fileName = `${teamName}_成员列表_${timestamp}.csv`
        link.setAttribute('download', fileName)

        // 触发下载
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 释放 URL 对象
        URL.revokeObjectURL(url)

        toastStore.success(`成功导出 ${exportData.length} 位成员的信息到文件：${fileName}`)
      } catch (error) {
        console.error('导出成员列表失败:', error)
        toastStore.error('导出失败，请稍后重试')
      } finally {
        exportLoading.value = false
      }
    }

    const bulkManageMembers = () => {
      toastStore.info('批量管理功能开发中...')
    }





    // 贡献度相关方法
    const showContributionDetails = () => {
      toastStore.info('贡献度详情功能开发中...')
    }

    // 内容相关方法
    const setContentFilter = (filter) => {
      activeContentFilter.value = filter
    }

    const getContentTypeLabel = (type) => {
      const labels = {
        article: '文章',
        tool: '工具',
        design: '设计',
        video: '视频'
      }
      return labels[type] || '其他'
    }

    const openContent = (content) => {
      toastStore.info(`打开内容: ${content.title}`)
    }

    // 成员相关方法
    const setMemberFilter = (filter) => {
      activeMemberFilter.value = filter
    }



    const editMemberRole = (member) => {
      editingMember.value = member
      showEditRoleModal.value = true
    }

    const closeEditRoleModal = () => {
      showEditRoleModal.value = false
      editingMember.value = null
    }

    const handleMemberRoleUpdated = (data) => {
      // 更新本地成员数据
      const member = allMembers.value.find(m => m.id === data.memberId)
      if (member) {
        member.role = data.newRole
      }
      toastStore.success('成员角色更新成功')
    }

    // 邀请成员相关方法
    const openInviteModal = () => {
      showInviteModal.value = true
      userSearchQuery.value = ''
      searchResults.value = []
      selectedUsers.value = []
      inviteRole.value = 'member'
      inviteMessage.value = ''
    }

    const closeInviteModal = () => {
      showInviteModal.value = false
      userSearchQuery.value = ''
      searchResults.value = []
      selectedUsers.value = []
      inviteRole.value = 'member'
      inviteMessage.value = ''
    }

    const searchUsers = async () => {
      if (!userSearchQuery.value.trim()) {
        searchResults.value = []
        return
      }

      searchLoading.value = true
      try {
        // 调用真实的用户搜索API
        const users = await userService.searchUsers(userSearchQuery.value.trim(), 10)

        if (users && Array.isArray(users)) {
          // 过滤已经是团队成员的用户
          const existingMemberIds = allMembers.value.map(m => m.id)
          searchResults.value = users
            .filter(user => !existingMemberIds.includes(user.id))
            .map(user => ({
              id: user.id,
              name: user.displayName || user.username,
              email: user.email,
              avatar: user.avatar || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.username}`,
              title: user.title || '用户',
              department: user.department,
              bio: user.bio,
              tags: user.tags || [],
              isOnline: Math.random() > 0.5 // 随机在线状态，实际应该从API获取
            }))
        } else {
          searchResults.value = []
        }
      } catch (error) {
        console.error('搜索用户失败:', error)
        toastStore.error('搜索用户失败，请重试')
        searchResults.value = []
      } finally {
        searchLoading.value = false
      }
    }

    const selectUser = (user) => {
      if (!selectedUsers.value.find(u => u.id === user.id)) {
        selectedUsers.value.push(user)
      }
      userSearchQuery.value = ''
      searchResults.value = []
    }

    const removeSelectedUser = (user) => {
      const index = selectedUsers.value.findIndex(u => u.id === user.id)
      if (index > -1) {
        selectedUsers.value.splice(index, 1)
      }
    }

    const sendInvitations = async () => {
      if (selectedUsers.value.length === 0) return

      inviteLoading.value = true
      try {
        // 调用真实的邀请API - 直接使用底层api获取完整响应
        const userIds = selectedUsers.value.map(user => user.id)
        const { api } = await import('@/utils/api')
        const result = await api.post(`/v1/teams/${team.id}/invite`, {
          userIds,
          message: inviteMessage.value || '邀请您加入我们的团队！',
          role: inviteRole.value
        })

        // 处理成功响应 - 检查code字段，200状态码表示请求成功
        if (result && (result.code === 200 || result.code === '200')) {
          // 无论data是true还是false，只要code是200就表示操作成功
          toastStore.success(`已向 ${selectedUsers.value.length} 位用户发送邀请`)

          // 关闭邀请弹窗
          closeInviteModal()

          // 刷新成员列表
          await loadTeamMembers(team.id)

          // 更新团队成员数量
          team.membersCount = allMembers.value.length
        } else {
          // 显示具体的错误信息，但不关闭弹窗
          const errorMessage = result?.message || '发送邀请失败'
          toastStore.error(errorMessage)
        }
      } catch (error) {
        console.error('邀请失败:', error)
        // 显示错误信息，但不关闭弹窗
        toastStore.error('发送邀请失败，请稍后重试')
      } finally {
        inviteLoading.value = false
      }
    }

    // 日历相关方法
    const openCalendar = () => {
      showCalendarModal.value = true
      loadCalendarEvents()
    }

    const closeCalendar = () => {
      showCalendarModal.value = false
    }

    const loadCalendarEvents = () => {
      // 模拟加载日历事件
      calendarEvents.value = [
        {
          id: 1,
          title: '团队周会',
          date: '2024-01-22',
          time: '10:00',
          type: 'meeting',
          participants: ['张三', '李四']
        },
        {
          id: 2,
          title: '项目评审',
          date: '2024-01-23',
          time: '14:00',
          type: 'review',
          participants: ['王五', '赵六']
        },
        {
          id: 3,
          title: '技术分享',
          date: '2024-01-25',
          time: '16:00',
          type: 'sharing',
          participants: ['张三', '钱七']
        }
      ]
    }

    const selectDate = (date) => {
      selectedDate.value = date
    }

    const getEventsForDate = (date) => {
      const dateStr = date.toISOString().split('T')[0]
      return calendarEvents.value.filter(event => event.date === dateStr)
    }

    const formatCalendarMonth = (date) => {
      return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })
    }

    const formatSelectedDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }

    const previousMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() - 1)
      currentDate.value = newDate
    }

    const nextMonth = () => {
      const newDate = new Date(currentDate.value)
      newDate.setMonth(newDate.getMonth() + 1)
      currentDate.value = newDate
    }

    const getEventIcon = (type) => {
      const icons = {
        meeting: 'fas fa-users',
        review: 'fas fa-clipboard-check',
        sharing: 'fas fa-share-alt',
        deadline: 'fas fa-clock',
        milestone: 'fas fa-flag'
      }
      return icons[type] || 'fas fa-calendar'
    }

    const createEvent = () => {
      toastStore.info('创建事项功能开发中...')
    }

    // 成员焦点滑动
    const scrollToMembers = () => {
      setActiveTab('members')
      // 等待DOM更新后滚动
      nextTick(() => {
        const membersSection = document.querySelector('.tab-content')
        if (membersSection) {
          membersSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      })
    }

    // 简化的事件处理方法
    const showContent = () => setActiveTab('content')
    const inviteMembers = openInviteModal
    // 快速工具方法 - 已隐藏
    /*
    const openTool = (tool) => {
      if (tool === 'calendar') {
        openCalendar()
      } else {
        toastStore.info(`${tool} 工具功能开发中...`)
      }
    }
    */
    const createContent = () => toastStore.info('创建内容功能开发中...')
    const editTeam = () => {
      showEditTeamModal.value = true
    }

    const closeEditTeamModal = () => {
      showEditTeamModal.value = false
    }

    const handleTeamUpdated = (updatedData) => {
      // 更新本地团队数据
      Object.assign(team, updatedData)
      toastStore.success('团队信息更新成功')
    }


    // 加载团队成员数据
    const loadTeamMembers = async (teamId) => {
      try {
        console.log('开始加载团队成员，teamId:', teamId)
        const membersData = await teamService.getTeamMembers(teamId, { page: 1, pageSize: 100 })

        if (membersData && membersData.list) {
          // 转换后端数据格式为前端需要的格式
          allMembers.value = membersData.list.map(member => {
            // 将字符串角色转换为数字角色值
            let roleValue = 0 // 默认为成员
            if (member.role === 'admin') {
              roleValue = 1
            } else if (member.role === 'owner' || member.role === 'creator') {
              roleValue = 2
            }

            return {
              id: member.userId,
              userId: member.userId, // 确保有userId字段
              name: member.displayName || member.username || '未知用户',
              email: member.email || '',
              avatar: member.avatarUrl || `https://api.dicebear.com/7.x/avataaars/svg?seed=${member.username || member.userId}`,
              role: member.role || 'member',
              roleValue: roleValue, // 数字角色值
              joinedAt: member.joinedAt || new Date().toISOString(),
              contributionScore: member.achievements?.totalViews || 0,
              isOnline: Math.random() > 0.5, // 随机在线状态，实际应该从API获取
              achievements: member.achievements || {
                articlesPublished: 0,
                totalViews: 0,
                totalLikes: 0,
                totalFavorites: 0
              }
            }
          })

          console.log('团队成员加载完成:', allMembers.value.length, '个成员')
        } else {
          // 如果没有从后端获取到数据，添加一些测试数据
          allMembers.value = [
            {
              id: 'user1',
              name: '张三',
              email: '<EMAIL>',
              avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=zhangsan',
              role: 'member',
              roleValue: 0,
              joinedAt: '2024-01-15T10:00:00Z',
              contributionScore: 85,
              isOnline: true,
              achievements: {
                articlesPublished: 5,
                totalViews: 1200,
                totalLikes: 45,
                totalFavorites: 12
              }
            },
            {
              id: 'user2',
              name: '李四',
              email: '<EMAIL>',
              avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=lisi',
              role: 'admin',
              roleValue: 1,
              joinedAt: '2024-01-10T09:00:00Z',
              contributionScore: 120,
              isOnline: false,
              achievements: {
                articlesPublished: 8,
                totalViews: 2100,
                totalLikes: 78,
                totalFavorites: 25
              }
            },
            {
              id: 'user3',
              name: '王五',
              email: '<EMAIL>',
              avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=wangwu',
              role: 'member',
              roleValue: 0,
              joinedAt: '2024-01-20T14:30:00Z',
              contributionScore: 65,
              isOnline: true,
              achievements: {
                articlesPublished: 3,
                totalViews: 800,
                totalLikes: 32,
                totalFavorites: 8
              }
            }
          ]
          console.log('使用测试成员数据')
        }
      } catch (err) {
        console.error('加载团队成员失败:', err)
        allMembers.value = []
        // 成员加载失败不影响主要功能，只记录错误
      }
    }

    // 加载团队数据
    const loadTeamData = async () => {
      try {
        loading.value = true
        error.value = null

        const teamId = route.params.id
        if (!teamId) {
          throw new Error('团队ID不存在')
        }

        // 并行加载团队详情和成员数据
        const [teamData] = await Promise.all([
          teamService.getTeamProfile(teamId),
          loadTeamMembers(teamId)
        ])
        
        // 处理嵌套的数据结构
        const basicInfo = teamData.basicInfo || {}

        // 更新团队数据
        Object.assign(team, {
          id: basicInfo.teamId || teamId,
          name: basicInfo.name || '未命名团队',
          description: basicInfo.description || '',
          avatar: basicInfo.avatarUrl,
          isPublic: basicInfo.privacy === 'public',
          isActive: true, // 假设团队是活跃的
          isMember: teamData.isMember !== undefined ? teamData.isMember : true, // 从后端获取用户是否为成员，默认为true用于测试
          isStarred: teamData.isStarred || false, // 从后端获取收藏状态
          isVerified: false, // 应该从后端获取
          location: '', // 后端暂未提供
          createdAt: basicInfo.createdAt,
          membersCount: basicInfo.memberCount || 0,
          contentCount: basicInfo.contentCount,

          tags: basicInfo.tags || [],
          // 用户角色信息 - 强制设为管理员用于测试
          userRole: 1, // 0=成员, 1=管理员, 2=创建者，强制设为管理员用于测试
          userRoleText: '管理员'
        })

        // 从成员列表中获取当前用户的角色信息
        const currentUserId = userStore.currentUserId
        if (currentUserId && allMembers.value.length > 0) {
          const currentUserMember = allMembers.value.find(member =>
            member.id == currentUserId || member.userId == currentUserId
          )
          if (currentUserMember) {
            team.userRole = currentUserMember.roleValue
            team.userRoleText = getRoleLabel(currentUserMember.role)
            console.log('从成员列表中获取到当前用户角色:', {
              userId: currentUserId,
              role: currentUserMember.role,
              roleValue: currentUserMember.roleValue,
              roleText: team.userRoleText
            })
          }
        }

        console.log('团队数据加载完成:', team)

      } catch (err) {
        console.error('加载团队数据失败:', err)
        error.value = err.message || '加载失败'
        toastStore.error('加载团队数据失败')
      } finally {
        loading.value = false
      }
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadTeamData()
      loadTeamSpaces()
    })
    
    return {
      // 响应式数据
      loading,
      error,
      joinLoading,
      showDropdown,
      showSpaceSelector,
      showMemberModal,
      showMemberActions,

      showInviteModal,
      showEditRoleModal,
      showEditTeamModal,
      editingMember,
      showCalendarModal,
      activeTab,

      activeContentFilter,
      activeMemberFilter,
      contentView,
      searchQuery,
      spaceSearchQuery,
      memberSearchQuery,

      userSearchQuery,
      searchLoading,
      inviteLoading,
      exportLoading,
      searchResults,
      selectedUsers,
      inviteRole,
      inviteMessage,
      currentDate,
      selectedDate,
      calendarEvents,
      team,
      userStore,
      mySpaces,
      otherSpaces,
      onlineMembers,
      allMembers,
      topContributors,
      maxContribution,
      recentActivities,
      tabs,
      teamDynamics,
      popularContent,
      teamAchievements,

      contentFilters,
      memberFilters,

      // 计算属性
      isAdmin,
      isCreator,
      canCreate,
      filteredMySpaces,
      filteredOtherSpaces,
      hasMoreSpaces,
      filteredMembers,

      filteredContent,
      filteredMembersForTab,
      adminCount,
      weekdays,
      calendarDates,

      // 方法
      formatDate,
      formatTime,
      getContentIcon,
      getRankClass,

      goBack,
      toggleStar,
      shareTeam,
      toggleDropdown,
      toggleSpaceSelector,
      switchSpace,
      joinSpace,
      showAllSpaces,
      createNewSpace,
      viewMemberProfile,
      joinTeam,
      setActiveTab,

      closeMemberModal,
      toggleMemberActions,
      getRoleLabel,
      canManageMember,
      canRemoveMember,
      updateMemberRole,
      removeMember,
      exportMemberList,
      bulkManageMembers,

      showContributionDetails,
      setContentFilter,
      getContentTypeLabel,
      openContent,
      setMemberFilter,
      getRoleIcon,
      editMemberRole,
      closeEditRoleModal,
      handleMemberRoleUpdated,
      openInviteModal,
      closeInviteModal,
      searchUsers,
      selectUser,
      removeSelectedUser,
      sendInvitations,
      openCalendar,
      closeCalendar,
      loadCalendarEvents,
      selectDate,
      getEventsForDate,
      formatCalendarMonth,
      formatSelectedDate,
      previousMonth,
      nextMonth,
      getEventIcon,
      createEvent,
      scrollToMembers,
      showContent,
      inviteMembers,
      createContent,
      editTeam,
      closeEditTeamModal,
      handleTeamUpdated,

      loadTeamSpaces
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
.team-space-detail {
  background: #f8f9fa;
  min-height: 100vh;
}

.container {
  max-width: 1600px; /* 与个人空间保持一致 */
  margin: 0 auto;
  padding: 0 24px; /* 优化容器内边距 */
}

/* 顶部导航栏 */
.team-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 20px 0; /* 增加导航栏高度 */
}

.header-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 24px; /* 增加间距 */
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #495057;
  padding: 10px 18px; /* 增加按钮内边距 */
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: #e9ecef;
  transform: translateX(-2px);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加面包屑间距 */
  font-size: 14px;
  color: #6c757d;
}

/* 空间选择器 */
.space-selector {
  position: relative;
}

.current-space {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 6px 12px; /* 增加按钮内边距 */
  border-radius: 6px;
  transition: all 0.3s ease;
}

.current-space:hover {
  background: #f8f9fa;
}

.space-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 320px;
  max-width: 400px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.space-search {
  position: relative;
  padding: 20px; /* 增加搜索区域内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.space-search i {
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

.space-search input {
  width: 100%;
  padding: 10px 12px 10px 36px; /* 增加输入框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: #f8f9fa;
}

.space-search input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
}

.space-list {
  max-height: 400px;
  overflow-y: auto;
}

.space-section {
  padding: 16px 0; /* 增加区块内边距 */
}

.space-section h4 {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 12px 0; /* 增加标题下边距 */
  padding: 0 20px;
}

.space-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px; /* 增加空间项内边距 */
  cursor: pointer;
  transition: background 0.3s ease;
}

.space-item:hover {
  background: #f8f9fa;
}

.space-item.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.space-avatar {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
}

.space-info {
  flex: 1;
}

.space-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.space-meta {
  font-size: 12px;
  color: #6c757d;
}

.space-status {
  color: #4CAF50;
  font-size: 8px;
}

.space-actions {
  display: flex;
  gap: 4px;
}

.join-btn {
  width: 24px;
  height: 24px;
  background: #667eea;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.join-btn:hover {
  background: #5a6fd8;
  transform: scale(1.1);
}

.show-more-spaces {
  width: 100%;
  padding: 16px 20px; /* 增加按钮内边距 */
  background: none;
  border: none;
  color: #667eea;
  font-size: 13px;
  cursor: pointer;
  transition: background 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.show-more-spaces:hover {
  background: #f8f9fa;
}

.space-actions-footer {
  padding: 16px 20px; /* 增加底部区域内边距 */
  border-top: 1px solid #f1f3f4;
  background: #f8f9fa;
}

.create-space-btn {
  width: 100%;
  padding: 12px 16px; /* 增加按钮内边距 */
  background: #667eea;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.create-space-btn:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px; /* 增加操作按钮间距 */
}

.action-btn {
  width: 40px; /* 增加按钮尺寸 */
  height: 40px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.starred {
  background: #ffc107;
  border-color: #ffc107;
  color: white;
}

.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 140px; /* 增加下拉菜单宽度 */
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-menu button {
  display: block;
  width: 100%;
  padding: 14px 18px; /* 增加菜单项内边距 */
  background: none;
  border: none;
  color: #495057;
  font-size: 14px;
  text-align: left;
  cursor: pointer;
  transition: background 0.3s ease;
}

.dropdown-menu button:hover {
  background: #f8f9fa;
}

/* 团队英雄区域 */
.team-hero {
  position: relative;
  padding: 80px 0; /* 增加英雄区域内边距 */
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  background-size: 100px 100px;
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 48px; /* 增加英雄区域内容间距 */
  align-items: center;
  color: white;
}

.team-profile {
  display: flex;
  gap: 28px; /* 增加头像和信息间距 */
  align-items: flex-start;
}

.team-avatar {
  position: relative;
  width: 120px; /* 增加头像尺寸 */
  height: 120px;
  border-radius: 16px;
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.team-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 42px; /* 增加占位符图标尺寸 */
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 18px; /* 增加状态指示器尺寸 */
  height: 18px;
  background: #4CAF50;
  border-radius: 50%;
  border: 3px solid white;
}

.team-info {
  flex: 1;
}

.team-badges {
  display: flex;
  gap: 10px; /* 增加徽章间距 */
  margin-bottom: 16px; /* 增加徽章下边距 */
}

.badge {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加徽章内容间距 */
  padding: 6px 12px; /* 增加徽章内边距 */
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.public {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.badge.private {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.badge.verified {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.team-name {
  font-size: 36px; /* 增加团队名称字体大小 */
  font-weight: 700;
  margin: 0 0 16px 0; /* 增加下边距 */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.team-description {
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 20px 0; /* 增加下边距 */
  opacity: 0.9;
  max-width: 500px;
}

.team-meta {
  display: flex;
  gap: 24px; /* 增加元数据间距 */
  margin-bottom: 20px; /* 增加下边距 */
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px; /* 增加图标和文字间距 */
  font-size: 14px;
  opacity: 0.8;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 增加标签间距 */
}

.tag {
  padding: 6px 12px; /* 增加标签内边距 */
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 团队统计和操作 */
.team-stats {
  display: flex;
  gap: 20px; /* 增加统计卡片间距 */
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 24px; /* 增加统计卡片内边距 */
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px; /* 增加最小宽度 */
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 28px; /* 增加数字字体大小 */
  font-weight: 700;
  line-height: 1;
  margin-bottom: 6px; /* 增加下边距 */
}

.stat-label {
  font-size: 13px; /* 增加标签字体大小 */
  opacity: 0.8;
  font-weight: 500;
}

.team-actions {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加操作按钮间距 */
  min-width: 220px; /* 增加最小宽度 */
}

.btn {
  padding: 14px 24px; /* 增加按钮内边距 */
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: white;
  color: #667eea;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-sm {
  padding: 10px 18px; /* 增加小按钮内边距 */
  font-size: 13px;
}

/* 主要内容区域 */
.team-main {
  padding: 48px 0; /* 增加主要内容区域内边距 */
}

.content-layout {
  display: grid;
  grid-template-columns: 360px 1fr; /* 参考个人空间，增加侧边栏宽度 */
  gap: 48px; /* 增加布局间距 */
}

/* 侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 28px; /* 增加组件间距 */
}

.widget {
  background: white;
  border-radius: 12px;
  padding: 24px; /* 增加组件内边距 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 10px; /* 增加标题图标间距 */
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 20px 0; /* 增加标题下边距 */
  color: #2c3e50;
}

.online-dot {
  color: #4CAF50;
  font-size: 8px;
}

/* 成员列表 */
.members-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加成员项间距 */
}

.member-item {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加成员项内容间距 */
  padding: 12px; /* 增加成员项内边距 */
  border-radius: 8px;
  transition: background 0.3s ease;
}

.member-item:hover {
  background: #f8f9fa;
}

.member-avatar {
  width: 40px; /* 增加头像尺寸 */
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.member-avatar.clickable,
.member-name.clickable,
.activity-user.clickable,
.contributor-name.clickable,
.contributor-avatar.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.member-avatar.clickable:hover,
.contributor-avatar.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.member-name.clickable:hover,
.activity-user.clickable:hover,
.contributor-name.clickable:hover {
  color: #667eea;
  text-decoration: underline;
}

.member-info {
  flex: 1;
}

.member-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.member-status {
  font-size: 12px;
  color: #6c757d;
}

.view-all {
  padding: 12px; /* 增加按钮内边距 */
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #667eea;
  font-size: 13px; /* 增加字体大小 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all:hover {
  background: #e9ecef;
}

/* 成员贡献度 */
.contribution-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加贡献项间距 */
}

.contribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px; /* 增加贡献项内边距 */
  border-radius: 8px;
  transition: background 0.3s ease;
}

.contribution-item:hover {
  background: #f8f9fa;
}

.rank-badge {
  width: 28px; /* 增加排名徽章尺寸 */
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.rank-badge.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
}

.rank-badge.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
  color: #666;
}

.rank-badge.bronze {
  background: linear-gradient(135deg, #cd7f32, #daa520);
  color: #5d3a00;
}

.rank-badge.normal {
  background: #e9ecef;
  color: #6c757d;
}

.contributor-avatar {
  width: 36px; /* 增加贡献者头像尺寸 */
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.contributor-info {
  flex: 1;
}

.contributor-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.contribution-score {
  font-size: 12px;
  color: #6c757d;
}

.contribution-chart {
  width: 60px; /* 增加图表宽度 */
}

.progress-bar {
  height: 6px; /* 增加进度条高度 */
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 快速工具 */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px; /* 增加工具按钮间距 */
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px; /* 增加图标和文字间距 */
  padding: 16px 12px; /* 增加工具按钮内边距 */
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-btn:hover {
  background: #e9ecef;
  color: #667eea;
  transform: translateY(-1px);
}

.tool-btn i {
  font-size: 18px; /* 增加工具图标尺寸 */
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加活动项间距 */
}

.activity-item {
  display: flex;
  gap: 14px; /* 增加活动项内容间距 */
  padding: 12px; /* 增加活动项内边距 */
  border-radius: 6px;
  transition: background 0.3s ease;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  width: 32px; /* 增加活动图标容器尺寸 */
  height: 32px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 14px; /* 增加图标尺寸 */
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 13px;
  color: #2c3e50;
  line-height: 1.4;
  margin-bottom: 4px; /* 增加下边距 */
}

.activity-time {
  font-size: 11px;
  color: #6c757d;
}

/* 主内容区 */
.main-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

/* 内容导航 */
.content-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0 24px; /* 增加导航内边距 */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-tabs {
  display: flex;
  gap: 0;
}

.nav-tab {
  position: relative;
  padding: 20px 24px; /* 增加标签内边距 */
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px; /* 增加图标和文字间距 */
  border-bottom: 3px solid transparent;
}

.nav-tab:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.nav-tab.active {
  color: #667eea;
  background: white;
  border-bottom-color: #667eea;
}

.tab-count {
  background: #e9ecef;
  color: #6c757d;
  padding: 3px 8px; /* 增加计数徽章内边距 */
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
}

.nav-tab.active .tab-count {
  background: #667eea;
  color: white;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 16px; /* 增加导航操作间距 */
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 14px; /* 调整搜索图标位置 */
  color: #6c757d;
  font-size: 14px;
}

.search-box input {
  padding: 10px 14px 10px 40px; /* 增加搜索框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  width: 220px; /* 增加搜索框宽度 */
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 标签内容 */
.tab-content {
  padding: 28px; /* 增加标签内容内边距 */
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 概览网格 */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); /* 增加最小列宽 */
  gap: 24px; /* 增加网格间距 */
}

.overview-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 24px; /* 增加卡片内边距 */
  border: 1px solid #e9ecef;
}

.overview-card h4 {
  margin: 0 0 20px 0; /* 增加标题下边距 */
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

/* 动态列表 */
.dynamic-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加动态项间距 */
}

.dynamic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0; /* 增加动态项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.dynamic-item:last-child {
  border-bottom: none;
}

.dynamic-text {
  font-size: 14px;
  color: #495057;
}

.dynamic-time {
  font-size: 12px;
  color: #6c757d;
}

/* 内容列表 */
.content-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加内容项间距 */
}

.content-item {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加内容项内容间距 */
  padding: 12px 0; /* 增加内容项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.content-item:last-child {
  border-bottom: none;
}

.content-item i {
  color: #667eea;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.content-title {
  flex: 1;
  font-size: 14px;
  color: #495057;
}

.content-stats {
  font-size: 12px;
  color: #6c757d;
}

/* 团队成就 */
.achievements-list {
  display: flex;
  flex-direction: column;
  gap: 16px; /* 增加成就项间距 */
}

.achievement-item {
  display: flex;
  gap: 14px; /* 增加成就项内容间距 */
  padding: 12px 0; /* 增加成就项内边距 */
  border-bottom: 1px solid #f1f3f4;
}

.achievement-item:last-child {
  border-bottom: none;
}

.achievement-icon {
  width: 32px; /* 增加成就图标容器尺寸 */
  height: 32px;
  background: #667eea;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px; /* 增加图标尺寸 */
  flex-shrink: 0;
}

.achievement-info {
  flex: 1;
}

.achievement-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.achievement-desc {
  font-size: 12px;
  color: #6c757d;
}

/* 内容占位符 */
.content-placeholder {
  text-align: center;
  padding: 80px 20px; /* 增加占位符内边距 */
  color: #6c757d;
}

.content-placeholder i {
  font-size: 56px; /* 增加占位符图标尺寸 */
  margin-bottom: 20px; /* 增加下边距 */
  opacity: 0.5;
}

.content-placeholder h3 {
  font-size: 20px; /* 增加标题字体大小 */
  margin: 0 0 12px 0; /* 增加下边距 */
  color: #495057;
}

.content-placeholder p {
  margin: 0;
  font-size: 14px;
}

/* 成员管理模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.member-management-modal {
  width: 100%;
  max-width: 1000px; /* 增加模态框最大宽度 */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px; /* 增加头部内边距 */
  border-bottom: 1px solid #e9ecef;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px; /* 增加标题字体大小 */
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  width: 36px; /* 增加关闭按钮尺寸 */
  height: 36px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  flex: 1;
  overflow: auto;
  padding: 28px; /* 增加主体内边距 */
}

/* 成员管理工具栏 */
.member-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px; /* 增加下边距 */
  gap: 20px; /* 增加间距 */
}

.search-members {
  position: relative;
  min-width: 280px;
  max-width: 400px;
}

.search-members i {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
  z-index: 1;
}

.search-members input {
  width: 100%;
  padding: 12px 40px 12px 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: #f8fafc;
  color: #374151;
  transition: all 0.3s ease;
}

.search-members input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-members input::placeholder {
  color: #9ca3af;
  font-size: 14px;
}

.clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  z-index: 1;
}

.clear-search:hover {
  color: #6b7280;
  background: #f3f4f6;
}

.member-actions {
  display: flex;
  gap: 12px; /* 增加操作按钮间距 */
}

/* 成员表格 */
.members-table {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 600;
  color: #495057;
  font-size: 13px;
}

.table-header > div {
  padding: 16px 20px; /* 增加表头内边距 */
  border-right: 1px solid #e9ecef;
}

.table-header > div:last-child {
  border-right: none;
}

.table-body {
  max-height: 400px; /* 限制表格主体高度 */
  overflow-y: auto;
}

.member-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.3s ease;
}

.member-row:hover {
  background: #f8f9fa;
}

.member-row:last-child {
  border-bottom: none;
}

.member-row > div {
  padding: 16px 20px; /* 增加表格单元格内边距 */
  border-right: 1px solid #f1f3f4;
  display: flex;
  align-items: center;
}

.member-row > div:last-child {
  border-right: none;
}

.col-member {
  display: flex;
  align-items: center;
  gap: 14px; /* 增加成员信息间距 */
}

.col-member .member-avatar {
  width: 40px; /* 增加表格中头像尺寸 */
  height: 40px;
}

.col-member .member-info {
  flex: 1;
}

.col-member .member-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px; /* 增加下边距 */
}

.col-member .member-email {
  font-size: 12px;
  color: #6c757d;
}

.col-member .member-status {
  display: flex;
  align-items: center;
  gap: 6px; /* 增加状态图标间距 */
  font-size: 11px;
  color: #6c757d;
  margin-left: 12px; /* 增加左边距 */
}

.col-member .member-status.online {
  color: #4CAF50;
}

.col-member .member-status i {
  font-size: 6px;
}

.role-select {
  padding: 6px 10px; /* 增加选择框内边距 */
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 13px;
  background: white;
  cursor: pointer;
}

.role-select:focus {
  outline: none;
  border-color: #667eea;
}

.role-select:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.contribution-score {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 6px; /* 增加下边距 */
}

.contribution-bar {
  width: 60px; /* 增加贡献度条宽度 */
  height: 6px; /* 增加高度 */
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.contribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 8px; /* 增加操作按钮间距 */
}

.action-btn {
  width: 32px; /* 增加操作按钮尺寸 */
  height: 32px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.action-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.action-btn.danger:hover {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .content-layout {
    grid-template-columns: 320px 1fr;
    gap: 40px;
  }
}

@media (max-width: 1200px) {
  .container {
    padding: 0 20px;
  }

  .content-layout {
    grid-template-columns: 300px 1fr;
    gap: 32px;
  }

  .hero-content {
    gap: 32px;
  }
}

@media (max-width: 992px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }

  .team-profile {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .team-stats {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .team-actions {
    align-items: center;
    max-width: 300px;
    margin: 0 auto;
  }

  .member-management-modal {
    max-width: 90vw;
  }

  .member-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-members {
    min-width: auto;
    max-width: none;
  }

  .member-actions {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .team-header {
    padding: 16px 0;
  }

  .team-hero {
    padding: 40px 0;
  }

  .team-name {
    font-size: 28px;
  }

  .team-description {
    font-size: 14px;
  }

  .team-meta {
    flex-direction: column;
    gap: 12px;
  }

  .team-stats {
    flex-direction: column;
    align-items: center;
  }

  .widget {
    padding: 20px;
  }

  .tab-content {
    padding: 20px;
  }

  .nav-tabs {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none;
  }

  .nav-tab {
    white-space: nowrap;
    padding: 16px 20px;
  }

  .search-box input {
    width: 180px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
  }

  .space-dropdown {
    min-width: 280px;
  }

  .table-header,
  .member-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .table-header > div,
  .member-row > div {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
    padding: 12px 16px;
  }

  .table-header > div:last-child,
  .member-row > div:last-child {
    border-bottom: none;
  }

  .col-member {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .col-member .member-status {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }

  .nav-left {
    gap: 16px;
  }

  .back-button {
    padding: 8px 12px;
  }

  .team-hero {
    padding: 30px 0;
  }

  .team-avatar {
    width: 80px;
    height: 80px;
  }

  .avatar-placeholder {
    font-size: 28px;
  }

  .team-name {
    font-size: 24px;
  }

  .team-description {
    font-size: 13px;
  }

  .team-meta {
    gap: 8px;
  }

  .team-stats {
    gap: 12px;
  }

  .stat-card {
    min-width: 100px;
    padding: 16px;
  }

  .stat-number {
    font-size: 20px;
  }

  .widget {
    padding: 16px;
  }

  .tab-content {
    padding: 16px;
  }

  .nav-actions {
    flex-direction: column;
    gap: 12px;
  }

  .search-box input {
    width: 150px;
  }

  .space-dropdown {
    min-width: 260px;
    left: -100px;
  }

  .modal-overlay {
    padding: 10px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .member-toolbar {
    gap: 12px;
  }

  .member-actions {
    flex-direction: column;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .contribution-list {
    gap: 12px;
  }

  .contribution-item {
    padding: 8px;
  }

  .rank-badge {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .contributor-avatar {
    width: 32px;
    height: 32px;
  }

  .contribution-chart {
    width: 40px;
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0 0 20px 0;
  font-size: 14px;
}



/* 热门内容样式 */
.popular-content-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popular-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.popular-item:hover {
  background: #f8f9fa;
}

.content-icon {
  width: 28px;
  height: 28px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  font-size: 12px;
  flex-shrink: 0;
}

.content-info {
  flex: 1;
}

.content-title {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
  line-height: 1.3;
}

.content-meta {
  display: flex;
  gap: 12px;
  font-size: 11px;
  color: #6c757d;
}

.views, .likes {
  display: flex;
  align-items: center;
  gap: 3px;
}

/* 团队成就紧凑样式 */
.achievements-compact {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.achievement-compact-item {
  display: flex;
  gap: 10px;
  align-items: center;
}

.achievement-compact-item .achievement-icon {
  width: 24px;
  height: 24px;
  background: #667eea;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  flex-shrink: 0;
}

.achievement-text {
  flex: 1;
}

.achievement-text .achievement-title {
  font-size: 12px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.achievement-text .achievement-desc {
  font-size: 10px;
  color: #6c757d;
  line-height: 1.3;
}

/* 内容管理样式 */
.content-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.content-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.content-view-toggle {
  display: flex;
  gap: 4px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 12px;
  background: #f8f9fa;
  border: none;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.view-btn.active {
  background: #667eea;
  color: white;
}

.content-container {
  display: grid;
  gap: 20px;
}

.content-container.grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.content-container.list {
  grid-template-columns: 1fr;
}

.content-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.content-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.content-thumbnail {
  position: relative;
  height: 160px;
  background: #f8f9fa;
  overflow: hidden;
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 32px;
}

.content-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.content-body {
  padding: 16px;
}

.content-body .content-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.content-description {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
}

.content-stats {
  display: flex;
  gap: 12px;
}

.content-stats .stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: #6c757d;
}

.content-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.content-tag {
  padding: 2px 6px;
  background: #f8f9fa;
  color: #6c757d;
  border-radius: 4px;
  font-size: 10px;
}

.content-time {
  font-size: 11px;
  color: #6c757d;
}

/* 成员管理样式 */
.members-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.members-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  flex-wrap: wrap;
}

.overview-stats {
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-item .stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.members-actions {
  display: flex;
  gap: 12px;
}

.members-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  white-space: nowrap;
}

.filter-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.filter-btn .filter-label {
  font-weight: 500;
}

.filter-btn .filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.filter-btn.active .filter-count {
  background: rgba(255, 255, 255, 0.25);
  color: white;
}

.filter-btn:not(.active) .filter-count {
  background: #e2e8f0;
  color: #64748b;
}

.search-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.member-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.member-card:hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.member-header {
  position: relative;
  text-align: center;
  margin-bottom: 8px;
}

.member-card .member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 6px;
}

.member-card .member-status {
  position: absolute;
  top: 30px;
  right: calc(50% - 24px);
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 9px;
  color: #6c757d;
}

.member-card .member-status.online {
  color: #4CAF50;
}

.member-card .member-status i {
  font-size: 6px;
}

.member-role-badge {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.member-role-badge.owner {
  background: #ffc107;
  color: #8b5a00;
}

.member-role-badge.admin {
  background: #667eea;
  color: white;
}

.member-role-badge.member {
  background: #e9ecef;
  color: #6c757d;
}

.member-card .member-info {
  text-align: center;
  margin-bottom: 8px;
}

.member-card .member-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 2px 0;
}

.member-card .member-email {
  font-size: 10px;
  color: #6c757d;
  margin: 0 0 6px 0;
}

.member-contribution {
  margin-bottom: 8px;
}

.contribution-label {
  font-size: 10px;
  color: #6c757d;
  margin-bottom: 3px;
  text-align: center;
}

.member-card .contribution-bar {
  height: 3px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 3px;
}

.member-card .contribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.member-card .contribution-score {
  font-size: 10px;
  color: #6c757d;
  text-align: center;
}

.member-meta {
  text-align: center;
  margin-bottom: 8px;
}

.join-date {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  font-size: 10px;
  color: #6c757d;
}

.member-card .member-actions {
  display: flex;
  justify-content: center;
  gap: 6px;
}

/* 数据洞察样式 */
.analytics-dashboard {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  margin: 0;
}

.metric-header i {
  color: #667eea;
  font-size: 18px;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.metric-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive {
  color: #4CAF50;
}

.metric-trend.negative {
  color: #f44336;
}

.metric-trend.neutral {
  color: #6c757d;
}

.analytics-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.analytics-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 24px;
}

.analytics-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.chart-placeholder {
  height: 200px;
  background: #f8f9fa;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
}

.chart-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
}

.content-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.distribution-label {
  min-width: 60px;
  font-size: 13px;
  color: #495057;
  font-weight: 500;
}

.distribution-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.distribution-fill {
  height: 100%;
  background: #667eea;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.distribution-value {
  min-width: 40px;
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  text-align: right;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .content-container.grid {
    grid-template-columns: 1fr;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .analytics-details {
    grid-template-columns: 1fr;
  }

  .members-overview {
    flex-direction: column;
    align-items: stretch;
  }

  .overview-stats {
    justify-content: space-around;
  }

  .content-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .members-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px;
  }

  .filter-section {
    justify-content: center;
  }

  .filter-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-section {
    width: 100%;
  }

  .search-members {
    min-width: auto;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .overview-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .content-thumbnail {
    height: 120px;
  }

  .member-card {
    padding: 10px;
  }

  .members-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
  }
}





/* 邀请成员模态框样式 */
.invite-member-modal {
  width: 100%;
  max-width: 700px;
}

.invite-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.user-search-container {
  position: relative;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper i {
  position: absolute;
  left: 12px;
  color: #6c757d;
  font-size: 14px;
}

.search-input-wrapper input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-loading {
  position: absolute;
  right: 12px;
  color: #667eea;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-result-item:hover {
  background: #f8f9fa;
}

.result-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.result-info {
  flex: 1;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.result-name {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
}

.result-status {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4CAF50;
  font-size: 10px;
}

.result-status .fa-circle {
  font-size: 6px;
}

.result-email {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.result-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.user-title {
  font-size: 11px;
  color: #495057;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

.user-department {
  font-size: 11px;
  color: #6c757d;
}

.result-tags {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.user-tag {
  font-size: 10px;
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 1px 4px;
  border-radius: 2px;
}

.result-bio {
  font-size: 11px;
  color: #6c757d;
  line-height: 1.3;
  max-height: 2.6em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
  font-size: 14px;
}

.selected-users {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 8px;
}

.selected-user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
}

.selected-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.selected-info {
  flex: 1;
}

.selected-name {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.selected-email {
  font-size: 11px;
  color: #6c757d;
}

.remove-user-btn {
  width: 24px;
  height: 24px;
  background: #dc3545;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.remove-user-btn:hover {
  background: #c82333;
  transform: scale(1.1);
}

.role-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.role-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.role-option:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.role-option input[type="radio"] {
  margin: 0;
}

.role-option input[type="radio"]:checked + .role-label {
  color: #667eea;
}

.role-label {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.role-label i {
  font-size: 18px;
  color: #6c757d;
  width: 20px;
  text-align: center;
}

.role-option input[type="radio"]:checked + .role-label i {
  color: #667eea;
}

.role-info {
  flex: 1;
}

.role-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.role-desc {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
}

/* 日历模态框样式 */
.calendar-modal {
  width: 100%;
  max-width: 900px;
}

.calendar-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.nav-btn {
  width: 36px;
  height: 36px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.calendar-grid {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.weekday-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.weekday {
  padding: 12px;
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
}

.dates-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.date-cell {
  min-height: 80px;
  padding: 8px;
  border-right: 1px solid #f1f3f4;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.date-cell:nth-child(7n) {
  border-right: none;
}

.date-cell:hover {
  background: #f8f9fa;
}

.date-cell.other-month {
  color: #9ca3af;
  background: #fafafa;
}

.date-cell.today {
  background: rgba(102, 126, 234, 0.1);
}

.date-cell.selected {
  background: #667eea;
  color: white;
}

.date-cell.has-events {
  background: rgba(76, 175, 80, 0.05);
}

.date-number {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
}

.date-events {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.event-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #667eea;
}

.event-dot.meeting {
  background: #667eea;
}

.event-dot.review {
  background: #28a745;
}

.event-dot.sharing {
  background: #ffc107;
}

.event-dot.deadline {
  background: #dc3545;
}

.more-events {
  font-size: 8px;
  color: #6c757d;
  margin-top: 2px;
}

.event-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.event-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 16px 0;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.event-item.meeting {
  border-left-color: #667eea;
}

.event-item.review {
  border-left-color: #28a745;
}

.event-item.sharing {
  border-left-color: #ffc107;
}

.event-time {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  min-width: 50px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.event-participants {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6c757d;
}

.event-type-icon {
  color: #6c757d;
  font-size: 14px;
}

.no-events {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 40px;
  color: #6c757d;
  font-size: 14px;
}

.event-actions {
  display: flex;
  justify-content: center;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .invite-member-modal,
  .calendar-modal {
    max-width: 95vw;
  }

  .role-selector {
    gap: 8px;
  }

  .role-option {
    padding: 12px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
  }

  .date-cell {
    min-height: 60px;
    padding: 4px;
  }

  .weekday {
    padding: 8px 4px;
    font-size: 10px;
  }

  .event-details {
    padding: 16px;
  }

  .event-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .selected-users {
    max-height: 150px;
  }

  .search-result-item {
    padding: 8px;
  }

  .result-avatar {
    width: 32px;
    height: 32px;
  }

  .date-cell {
    min-height: 50px;
    padding: 2px;
  }

  .date-number {
    font-size: 12px;
  }

  .event-dot {
    width: 4px;
    height: 4px;
  }
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
  color: #6c757d;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
  color: #6c757d;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  color: #dc3545;
  margin-bottom: 20px;
}

.error-state h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #343a40;
}

.error-state p {
  max-width: 500px;
}
</style>
