<template>
  <div v-if="visible && team" class="modal-overlay" @click.self="$emit('close')">
    <div class="team-preview-modal">
      <!-- 模态框头部 -->
      <div class="modal-header">
        <div class="team-header-info">
          <div class="team-avatar-large">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" />
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
            
            <!-- 状态标识 -->
            <div class="status-badges">
              <div v-if="team.isMember" class="status-badge member">
                <i class="fas fa-check"></i>
                <span>已加入</span>
              </div>
              <div v-else-if="team.isPublic" class="status-badge public">
                <i class="fas fa-globe"></i>
                <span>公开</span>
              </div>
              <div v-else class="status-badge private">
                <i class="fas fa-lock"></i>
                <span>私有</span>
              </div>
            </div>
          </div>
          
          <div class="team-basic-info">
            <h2 class="team-name">{{ team.name }}</h2>
            <p class="team-description">{{ team.description || '暂无描述' }}</p>
            
            <!-- 团队标签 -->
            <div v-if="team.tags && team.tags.length" class="team-tags">
              <span
                v-for="tag in team.tags"
                :key="tag"
                class="tag"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>

        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 模态框内容 -->
      <div class="modal-content">
        <!-- 团队统计 -->
        <div class="team-stats-section">
          <h3 class="section-title">团队数据</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon members">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ team.membersCount || 0 }}</div>
                <div class="stat-label">团队成员</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon articles">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ team.articlesCount || 0 }}</div>
                <div class="stat-label">发布文章</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon likes">
                <i class="fas fa-heart"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatNumber(team.likesCount || 0) }}</div>
                <div class="stat-label">获得点赞</div>
              </div>
            </div>
            
            <div class="stat-card">
              <div class="stat-icon views">
                <i class="fas fa-eye"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatNumber(team.viewsCount || 0) }}</div>
                <div class="stat-label">总浏览量</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队成员 -->
        <div v-if="team.members && team.members.length" class="team-members-section">
          <h3 class="section-title">团队成员 ({{ team.members.length }})</h3>
          <div class="members-list">
            <div
              v-for="member in team.members.slice(0, 8)"
              :key="member.userId"
              class="member-item"
            >
              <div class="member-avatar">
                <img v-if="member.avatar" :src="member.avatar" :alt="member.name" />
                <div v-else class="avatar-placeholder">
                  <i class="fas fa-user"></i>
                </div>
                <div v-if="member.role === 'owner'" class="role-badge owner">
                  <i class="fas fa-crown"></i>
                </div>
                <div v-else-if="member.role === 'admin'" class="role-badge admin">
                  <i class="fas fa-shield-alt"></i>
                </div>
              </div>
              <div class="member-info">
                <div class="member-name">{{ member.name || '未知用户' }}</div>
                <div class="member-role">{{ getRoleLabel(member.role) }}</div>
              </div>
            </div>
            
            <div v-if="team.members.length > 8" class="more-members">
              <div class="more-avatar">
                <span>+{{ team.members.length - 8 }}</span>
              </div>
              <div class="more-info">
                <div class="more-text">更多成员</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队信息 -->
        <div class="team-info-section">
          <h3 class="section-title">团队信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <i class="fas fa-calendar"></i>
              <span class="info-label">创建时间:</span>
              <span class="info-value">{{ formatDate(team.createdAt) }}</span>
            </div>
            <div v-if="team.lastActivityAt" class="info-item">
              <i class="fas fa-clock"></i>
              <span class="info-label">最近活跃:</span>
              <span class="info-value">{{ formatLastActivity(team.lastActivityAt) }}</span>
            </div>
            <div class="info-item">
              <i class="fas fa-globe-americas"></i>
              <span class="info-label">团队类型:</span>
              <span class="info-value">{{ team.isPublic ? '公开团队' : '私有团队' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 模态框底部 -->
      <div class="modal-footer">
        <div class="footer-actions">
          <button
            class="action-btn star-btn"
            :class="{ active: team.isStarred }"
            @click="$emit('star', team)"
          >
            <i class="fas fa-star"></i>
            {{ team.isStarred ? '已收藏' : '收藏' }}
          </button>
          
          <button class="action-btn share-btn" @click="handleShare">
            <i class="fas fa-share-alt"></i>
            分享
          </button>
        </div>

        <div class="primary-actions">
          <button class="btn-secondary" @click="$emit('close')">
            关闭
          </button>
          
          <button
            v-if="team.isMember"
            class="btn-primary enter"
            @click="handleEnterTeam"
          >
            <i class="fas fa-arrow-right"></i>
            进入团队
          </button>
          <button
            v-else
            class="btn-primary join"
            @click="$emit('join', team)"
          >
            <i class="fas fa-plus"></i>
            {{ team.isPublic ? '立即加入' : '申请加入' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useToastStore } from '@/stores/toast'

export default {
  name: 'TeamPreviewModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    team: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'join', 'star'],
  setup(props, { emit }) {
    const router = useRouter()
    const toastStore = useToastStore()

    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }

    const formatLastActivity = (dateString) => {
      if (!dateString) return '暂无活动'
      
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = now - date
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)
      
      if (diffHours < 1) {
        return '刚刚活跃'
      } else if (diffHours < 24) {
        return `${diffHours}小时前`
      } else if (diffDays < 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        })
      }
    }

    const getRoleLabel = (role) => {
      const roleMap = {
        'owner': '创建者',
        'admin': '管理员',
        'member': '成员'
      }
      return roleMap[role] || '成员'
    }

    const handleEnterTeam = () => {
      router.push(`/team-space/${props.team.id}`)
      emit('close')
    }

    const handleShare = async () => {
      const shareData = {
        title: props.team.name,
        text: props.team.description,
        url: `${window.location.origin}/team-space/${props.team.id}`
      }

      try {
        if (navigator.share) {
          await navigator.share(shareData)
        } else {
          await navigator.clipboard.writeText(shareData.url)
          toastStore.success('链接已复制到剪贴板')
        }
      } catch (error) {
        console.error('分享失败:', error)
        toastStore.error('分享失败')
      }
    }

    return {
      formatNumber,
      formatDate,
      formatLastActivity,
      getRoleLabel,
      handleEnterTeam,
      handleShare
    }
  }
}
</script>

<style scoped>
/* 🎨 团队预览模态框样式 */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.team-preview-modal {
  background: white;
  border-radius: 24px;
  max-width: 700px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== 模态框头部 ===== */
.modal-header {
  padding: 32px 32px 24px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
}

.team-header-info {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.team-avatar-large {
  position: relative;
  flex-shrink: 0;
}

.team-avatar-large > img,
.team-avatar-large > .avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 4px solid white;
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
}

.avatar-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
}

.status-badges {
  position: absolute;
  bottom: -8px;
  right: -8px;
  display: flex;
  gap: 4px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.status-badge.member {
  background: linear-gradient(135deg, #10b981, #059669);
}

.status-badge.public {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.status-badge.private {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

.team-basic-info {
  flex: 1;
  min-width: 0;
}

.team-name {
  font-size: 28px;
  font-weight: 800;
  color: #1a202c;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.team-description {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 16px 0;
  line-height: 1.6;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 6px 12px;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  color: #4b5563;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.close-btn {
  position: absolute;
  top: 24px;
  right: 24px;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 50%;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* ===== 模态框内容 ===== */
.modal-content {
  padding: 0 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 2px;
}

/* ===== 团队统计 ===== */
.team-stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-card {
  padding: 20px;
  background: #f8fafc;
  border-radius: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: #f1f5f9;
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-icon.members {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.stat-icon.articles {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon.likes {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-icon.views {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== 团队成员 ===== */
.team-members-section {
  margin-bottom: 32px;
}

.members-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.member-item,
.more-members {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.member-item:hover {
  border-color: #6366f1;
  background: #f8faff;
}

.member-avatar,
.more-avatar {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  flex-shrink: 0;
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.more-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 700;
}

.role-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  color: white;
}

.role-badge.owner {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.role-badge.admin {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.member-info,
.more-info {
  flex: 1;
  min-width: 0;
}

.member-name,
.more-text {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.member-role {
  font-size: 12px;
  color: #64748b;
}

/* ===== 团队信息 ===== */
.team-info-section {
  margin-bottom: 32px;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
}

.info-item i {
  color: #6366f1;
  font-size: 16px;
  width: 20px;
}

.info-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #1a202c;
  font-weight: 600;
}

/* ===== 模态框底部 ===== */
.modal-footer {
  padding: 24px 32px 32px;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
  border-radius: 0 0 24px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #64748b;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  border-color: #6366f1;
  color: #6366f1;
}

.star-btn.active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-color: transparent;
}

.primary-actions {
  display: flex;
  gap: 12px;
}

.btn-secondary,
.btn-primary {
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn-secondary {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn-secondary:hover {
  border-color: #6366f1;
  color: #6366f1;
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.btn-primary.enter {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-primary.enter:hover {
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 16px;
  }

  .team-preview-modal {
    border-radius: 20px;
    max-height: 95vh;
  }

  .modal-header {
    padding: 24px 24px 20px;
  }

  .team-header-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 16px;
  }

  .team-avatar-large > img,
  .team-avatar-large > .avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 16px;
  }

  .avatar-placeholder {
    font-size: 28px;
  }

  .team-name {
    font-size: 24px;
  }

  .team-description {
    font-size: 15px;
  }

  .close-btn {
    top: 20px;
    right: 20px;
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .modal-content {
    padding: 0 24px;
  }

  .section-title {
    font-size: 16px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .stat-card {
    padding: 16px;
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .stat-number {
    font-size: 20px;
  }

  .members-list {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .member-item,
  .more-members {
    padding: 10px;
  }

  .member-avatar,
  .more-avatar {
    width: 36px;
    height: 36px;
  }

  .modal-footer {
    padding: 20px 24px 24px;
    flex-direction: column;
    gap: 16px;
  }

  .footer-actions {
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .primary-actions {
    order: 1;
    width: 100%;
  }

  .btn-secondary,
  .btn-primary {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .modal-overlay {
    padding: 12px;
  }

  .team-preview-modal {
    border-radius: 16px;
  }

  .modal-header {
    padding: 20px 20px 16px;
  }

  .team-avatar-large > img,
  .team-avatar-large > .avatar-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 12px;
  }

  .avatar-placeholder {
    font-size: 20px;
  }

  .team-name {
    font-size: 20px;
  }

  .team-description {
    font-size: 14px;
  }

  .close-btn {
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .modal-content {
    padding: 0 20px;
  }

  .section-title {
    font-size: 15px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .stat-card {
    padding: 12px;
    gap: 10px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .stat-number {
    font-size: 18px;
  }

  .stat-label {
    font-size: 11px;
  }

  .member-item,
  .more-members {
    padding: 8px;
  }

  .member-avatar,
  .more-avatar {
    width: 32px;
    height: 32px;
  }

  .member-name,
  .more-text {
    font-size: 13px;
  }

  .member-role {
    font-size: 11px;
  }

  .info-item {
    padding: 12px;
  }

  .info-label,
  .info-value {
    font-size: 13px;
  }

  .modal-footer {
    padding: 16px 20px 20px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .btn-secondary,
  .btn-primary {
    padding: 10px 20px;
    font-size: 13px;
  }
}
</style>
