<template>
  <div class="team-card">
    <div class="card-background">
      <div class="gradient-overlay"></div>
    </div>
    
    <div class="card-content">
      <!-- 团队基本信息 -->
      <div class="team-info">
        <div class="avatar-section">
          <div class="team-avatar-container">
            <img v-if="team.avatar" :src="team.avatar" :alt="team.name" class="team-avatar">
            <div v-else class="avatar-placeholder">
              <i class="fas fa-users"></i>
            </div>
            <button v-if="canManage" class="edit-avatar-btn" @click="editAvatar">
              <i class="fas fa-camera"></i>
            </button>
          </div>
        </div>
        
        <div class="info-section">
          <div class="basic-info">
            <h2 class="team-name">{{ team.name }}</h2>
            <p class="team-description">{{ team.description || '这个团队还没有添加描述...' }}</p>
            <div class="team-meta">
              <span class="created-date">
                <i class="fas fa-calendar"></i>
                创建于 {{ formatDate(team.createdAt) }}
              </span>
              <span class="team-visibility">
                <i :class="team.isPublic ? 'fas fa-globe' : 'fas fa-lock'"></i>
                {{ team.isPublic ? '公开团队' : '私有团队' }}
              </span>
            </div>
          </div>
          
          <div class="action-buttons">
            <button v-if="canManage" class="btn btn-primary" @click="editTeam">
              <i class="fas fa-edit"></i>
              编辑团队
            </button>
            <button class="btn btn-outline" @click="shareTeam">
              <i class="fas fa-share"></i>
              分享团队
            </button>
            <button v-if="!canManage" class="btn btn-outline" @click="leaveTeam">
              <i class="fas fa-sign-out-alt"></i>
              退出团队
            </button>
          </div>
        </div>
      </div>
      
      <!-- 团队统计 -->
      <div class="team-stats">
        <div class="stat-item" @click="viewMembers">
          <div class="stat-number">{{ team.membersCount }}</div>
          <div class="stat-label">团队成员</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item" @click="viewContent">
          <div class="stat-number">{{ team.articlesCount }}</div>
          <div class="stat-label">发布文章</div>
        </div>
        <div class="stat-divider"></div>
        <div class="stat-item" @click="viewActivities">
          <div class="stat-number">{{ team.activitiesCount }}</div>
          <div class="stat-label">本周活动</div>
        </div>
      </div>

      <!-- 团队成就 -->
      <div v-if="achievements" class="team-achievements">
        <h4 class="achievements-title">团队成就</h4>
        <div class="achievements-grid">
          <div class="achievement-item reads">
            <div class="achievement-icon">
              <i class="fas fa-eye"></i>
            </div>
            <div class="achievement-content">
              <div class="achievement-number">{{ formatNumber(achievements.totalReads) }}</div>
              <div class="achievement-label">总阅读量</div>
            </div>
          </div>

          <div class="achievement-item likes">
            <div class="achievement-icon">
              <i class="fas fa-heart"></i>
            </div>
            <div class="achievement-content">
              <div class="achievement-number">{{ formatNumber(achievements.totalLikes) }}</div>
              <div class="achievement-label">总点赞数</div>
            </div>
          </div>

          <div class="achievement-item bookmarks">
            <div class="achievement-icon">
              <i class="fas fa-bookmark"></i>
            </div>
            <div class="achievement-content">
              <div class="achievement-number">{{ formatNumber(achievements.totalBookmarks) }}</div>
              <div class="achievement-label">总收藏数</div>
            </div>
          </div>

          <div class="achievement-item articles">
            <div class="achievement-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <div class="achievement-content">
              <div class="achievement-number">{{ achievements.totalArticles }}</div>
              <div class="achievement-label">发布文章</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 团队成员预览 -->
      <div v-if="!hideMembers" class="members-preview">
        <h4 class="preview-title">团队成员</h4>
        <div class="members-avatars">
          <div
            v-for="member in previewMembers"
            :key="member.id"
            class="member-avatar"
            :title="member.name"
          >
            <img v-if="member.avatar" :src="member.avatar" :alt="member.name">
            <div v-else class="member-placeholder">
              {{ member.name.charAt(0) }}
            </div>
          </div>
          <div v-if="team.membersCount > 5" class="more-members">
            +{{ team.membersCount - 5 }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useToastStore } from '../../stores/toast'

export default {
  name: 'TeamCard',
  props: {
    team: {
      type: Object,
      required: true
    },
    currentUserRole: {
      type: String,
      default: 'member'
    },
    hideMembers: {
      type: Boolean,
      default: false
    },
    achievements: {
      type: Object,
      default: null
    }
  },
  emits: ['edit-team', 'leave-team', 'view-members', 'view-content', 'view-activities'],
  setup(props, { emit }) {
    const toastStore = useToastStore()
    
    const canManage = computed(() => {
      return ['owner', 'admin'].includes(props.currentUserRole)
    })
    
    const previewMembers = computed(() => {
      return props.team.members ? props.team.members.slice(0, 5) : []
    })
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }

    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k'
      }
      return num.toString()
    }
    
    const editAvatar = () => {
      toastStore.info('头像编辑功能开发中...')
    }
    
    const editTeam = () => {
      emit('edit-team', props.team)
    }
    
    const shareTeam = () => {
      const teamUrl = `${window.location.origin}/team-space/${props.team.id}`
      navigator.clipboard.writeText(teamUrl).then(() => {
        toastStore.success('团队链接已复制到剪贴板')
      }).catch(() => {
        toastStore.error('复制失败，请手动复制链接')
      })
    }
    
    const leaveTeam = () => {
      emit('leave-team', props.team)
    }
    
    const viewMembers = () => {
      emit('view-members')
    }
    
    const viewContent = () => {
      emit('view-content')
    }
    
    const viewActivities = () => {
      emit('view-activities')
    }
    
    return {
      canManage,
      previewMembers,
      formatDate,
      formatNumber,
      editAvatar,
      editTeam,
      shareTeam,
      leaveTeam,
      viewMembers,
      viewContent,
      viewActivities
    }
  }
}
</script>

<style scoped>
.team-card {
  position: relative;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 30px;
}

.card-background {
  position: relative;
  height: 120px;
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8) 0%, rgba(59, 130, 246, 0.8) 100%);
}

.card-content {
  position: relative;
  padding: 0 30px 30px;
}

.team-info {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.avatar-section {
  flex-shrink: 0;
  margin-top: -40px;
}

.team-avatar-container {
  position: relative;
  width: 100px;
  height: 100px;
}

.team-avatar,
.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: 16px;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.team-avatar {
  object-fit: cover;
}

.avatar-placeholder {
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 32px;
}

.edit-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #8b5cf6;
  color: white;
  border: 2px solid white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-avatar-btn:hover {
  background: #7c3aed;
  transform: scale(1.1);
}

.info-section {
  flex: 1;
  padding-top: 20px;
}

.basic-info {
  margin-bottom: 20px;
}

.team-name {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.team-description {
  color: #374151;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 12px 0;
}

.team-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #6b7280;
}

.team-meta span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.team-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;
  margin-bottom: 25px;
}

.stat-item {
  text-align: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: #f9fafb;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: #e5e7eb;
  margin: 0 20px;
}

/* 团队成就样式 */
.team-achievements {
  padding: 25px 0;
  border-top: 1px solid #f3f4f6;
}

.achievements-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 20px 0;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid transparent;
}

.achievement-item.reads {
  border-left-color: #3b82f6;
}

.achievement-item.likes {
  border-left-color: #ef4444;
}

.achievement-item.bookmarks {
  border-left-color: #f59e0b;
}

.achievement-item.articles {
  border-left-color: #10b981;
}

.achievement-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.reads .achievement-icon {
  background: #3b82f6;
}

.likes .achievement-icon {
  background: #ef4444;
}

.bookmarks .achievement-icon {
  background: #f59e0b;
}

.articles .achievement-icon {
  background: #10b981;
}

.achievement-content {
  flex: 1;
}

.achievement-number {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 2px;
}

.achievement-label {
  font-size: 12px;
  color: #6b7280;
}

.members-preview {
  padding: 20px 0;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 15px 0;
}

.members-avatars {
  display: flex;
  align-items: center;
  gap: -8px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid white;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.member-avatar:hover {
  z-index: 2;
  transform: scale(1.1);
}

.member-avatar img,
.member-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-placeholder {
  background: #e0e7ff;
  color: #4338ca;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.more-members {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

@media (max-width: 768px) {
  .card-content {
    padding: 0 20px 20px;
  }
  
  .team-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }
  
  .avatar-section {
    margin-top: -50px;
  }
  
  .info-section {
    padding-top: 0;
  }
  
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .team-stats {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .stat-divider {
    display: none;
  }
  
  .team-meta {
    flex-direction: column;
    gap: 8px;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .achievement-item {
    padding: 12px;
  }

  .achievement-number {
    font-size: 16px;
  }
}
</style>
